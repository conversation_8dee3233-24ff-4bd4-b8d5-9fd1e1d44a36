{"version": 3, "sources": ["umi.14716139553398118451.hot-update.js", "src/services/user.ts"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/.umi/umi.ts?hmr',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='12394628753835775515';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/.umi/plugin-openapi/openapi.tsx\":[\"vendors\",\"src/.umi/plugin-openapi/openapi.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/friend/index.tsx\":[\"p__friend__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"common\",\"p__subscription__index\"],\"src/pages/team/detail/index.tsx\":[\"common\",\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"p__team__index\"],\"src/pages/test/profile-consolidated/index.tsx\":[\"src/pages/test/profile-consolidated/index.tsx\"],\"src/pages/user/index.tsx\":[\"common\",\"p__user__index\"],\"src/pages/user/login/index.tsx\":[\"p__user__login__index\"],\"src/pages/user/team-select/index.tsx\":[\"src/pages/user/team-select/index.tsx\"]});;\r\n  },\r\n);\r\n", "/**\n * 用户管理相关 API 服务\n */\n\nimport { apiRequest } from '@/utils/request';\nimport type {\n  UserProfileResponse,\n  UpdateUserProfileRequest,\n  UserPersonalStatsResponse,\n  UserProfileDetailResponse,\n} from '@/types/api';\n\n/**\n * 用户服务类\n */\nexport class UserService {\n  /**\n   * 获取当前用户资料\n   */\n  static async getUserProfile(): Promise<UserProfileResponse> {\n    const response = await apiRequest.get<UserProfileResponse>('/users/profile');\n    return response.data;\n  }\n\n  /**\n   * 更新用户资料\n   */\n  static async updateUserProfile(data: UpdateUserProfileRequest): Promise<UserProfileResponse> {\n    const response = await apiRequest.put<UserProfileResponse>('/users/profile', data);\n    return response.data;\n  }\n\n  /**\n   * 修改密码\n   */\n  static async changePassword(currentPassword: string, newPassword: string): Promise<void> {\n    const data: UpdateUserProfileRequest = {\n      currentPassword,\n      newPassword,\n    };\n    \n    const response = await apiRequest.put<void>('/users/profile', data);\n    return response.data;\n  }\n\n  /**\n   * 更新用户名\n   */\n  static async updateUserName(name: string): Promise<UserProfileResponse> {\n    const data: UpdateUserProfileRequest = {\n      name,\n    };\n    \n    const response = await apiRequest.put<UserProfileResponse>('/users/profile', data);\n    return response.data;\n  }\n\n  /**\n   * 验证当前密码\n   */\n  static async validateCurrentPassword(password: string): Promise<boolean> {\n    try {\n      const response = await apiRequest.post<boolean>('/users/validate-password', { password });\n      return response.data;\n    } catch {\n      return false;\n    }\n  }\n\n  /**\n   * 获取用户统计信息\n   */\n  static async getUserStats(): Promise<{\n    totalTeams: number;\n    createdTeams: number;\n    joinedTeams: number;\n    lastLoginTime: string;\n  }> {\n    // 这里可能需要后端提供专门的统计接口\n    // 暂时返回模拟数据\n    return {\n      totalTeams: 0,\n      createdTeams: 0,\n      joinedTeams: 0,\n      lastLoginTime: new Date().toISOString(),\n    };\n  }\n\n  /**\n   * 获取用户个人统计数据（车辆、人员、预警、告警）\n   */\n  static async getUserPersonalStats(): Promise<UserPersonalStatsResponse> {\n    const response = await apiRequest.get<UserPersonalStatsResponse>('/users/personal-stats');\n    return response.data;\n  }\n\n  /**\n   * 检查邮箱是否已被使用\n   */\n  static async checkEmailAvailable(email: string): Promise<boolean> {\n    try {\n      // 这里可能需要后端提供专门的检查接口\n      // 暂时返回 true\n      return true;\n    } catch {\n      return false;\n    }\n  }\n\n  /**\n   * 获取用户活动日志\n   */\n  static async getUserActivityLog(params?: {\n    startDate?: string;\n    endDate?: string;\n    limit?: number;\n  }): Promise<Array<{\n    id: number;\n    action: string;\n    description: string;\n    timestamp: string;\n    ipAddress?: string;\n    userAgent?: string;\n  }>> {\n    // 这里需要后端提供活动日志接口\n    // 暂时返回空数组\n    return [];\n  }\n\n  /**\n   * 导出用户数据\n   */\n  static async exportUserData(): Promise<Blob> {\n    // 这里需要后端提供数据导出接口\n    const response = await apiRequest.get('/users/export');\n    return response as unknown as Blob;\n  }\n\n  /**\n   * 删除用户账户\n   */\n  static async deleteAccount(password: string): Promise<void> {\n    const response = await apiRequest.delete<void>('/users/account', {\n      password,\n    });\n    return response.data;\n  }\n\n\n}\n\n// 导出默认实例\nexport default UserService;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uBACA;IACE,SAAS;;;;;;;;;;;;;gBCYA,WAAW;2BAAX;;gBAwIb,SAAS;gBACT,OAA2B;2BAA3B;;;;;4CApJ2B;;;;;;;;;YAWpB,MAAM;gBACX;;GAEC,GACD,aAAa,iBAA+C;oBAC1D,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAsB;oBAC3D,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,kBAAkB,IAA8B,EAAgC;oBAC3F,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAsB,kBAAkB;oBAC7E,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,eAAe,eAAuB,EAAE,WAAmB,EAAiB;oBACvF,MAAM,OAAiC;wBACrC;wBACA;oBACF;oBAEA,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAO,kBAAkB;oBAC9D,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,eAAe,IAAY,EAAgC;oBACtE,MAAM,OAAiC;wBACrC;oBACF;oBAEA,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAsB,kBAAkB;oBAC7E,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,wBAAwB,QAAgB,EAAoB;oBACvE,IAAI;wBACF,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CAAU,4BAA4B;4BAAE;wBAAS;wBACvF,OAAO,SAAS,IAAI;oBACtB,EAAE,OAAM;wBACN,OAAO;oBACT;gBACF;gBAEA;;GAEC,GACD,aAAa,eAKV;oBACD,oBAAoB;oBACpB,WAAW;oBACX,OAAO;wBACL,YAAY;wBACZ,cAAc;wBACd,aAAa;wBACb,eAAe,IAAI,OAAO,WAAW;oBACvC;gBACF;gBAEA;;GAEC,GACD,aAAa,uBAA2D;oBACtE,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAA4B;oBACjE,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,oBAAoB,KAAa,EAAoB;oBAChE,IAAI;wBACF,oBAAoB;wBACpB,YAAY;wBACZ,OAAO;oBACT,EAAE,OAAM;wBACN,OAAO;oBACT;gBACF;gBAEA;;GAEC,GACD,aAAa,mBAAmB,MAI/B,EAOG;oBACF,iBAAiB;oBACjB,UAAU;oBACV,OAAO,EAAE;gBACX;gBAEA;;GAEC,GACD,aAAa,iBAAgC;oBAC3C,iBAAiB;oBACjB,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAC;oBACtC,OAAO;gBACT;gBAEA;;GAEC,GACD,aAAa,cAAc,QAAgB,EAAiB;oBAC1D,MAAM,WAAW,MAAM,mBAAU,CAAC,MAAM,CAAO,kBAAkB;wBAC/D;oBACF;oBACA,OAAO,SAAS,IAAI;gBACtB;YAGF;gBAGA,WAAe;;;;;;;;;;;;;;;;;;;;;;;IDrJD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,uCAAsC;YAAC;YAAU;SAAsC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,8BAA6B;YAAC;SAAmB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;YAAS;SAAyB;QAAC,mCAAkC;YAAC;YAAS;SAAyB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,iDAAgD;YAAC;SAAgD;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,kCAAiC;YAAC;SAAwB;QAAC,wCAAuC;YAAC;SAAuC;IAAA;;AACh/B"}