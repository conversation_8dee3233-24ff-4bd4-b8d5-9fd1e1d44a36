{"version": 3, "sources": ["src_pages_test_profile-consolidated_index_tsx-async.6639027343615962924.hot-update.js", "src/pages/test/profile-consolidated/TodoManagement.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/test/profile-consolidated/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='6757539198085293395';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/.umi/plugin-openapi/openapi.tsx\":[\"vendors\",\"src/.umi/plugin-openapi/openapi.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/friend/index.tsx\":[\"p__friend__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"common\",\"p__subscription__index\"],\"src/pages/team/detail/index.tsx\":[\"common\",\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"p__team__index\"],\"src/pages/test/profile-consolidated/index.tsx\":[\"src/pages/test/profile-consolidated/index.tsx\"],\"src/pages/user/index.tsx\":[\"common\",\"p__user__index\"],\"src/pages/user/login/index.tsx\":[\"p__user__login__index\"],\"src/pages/user/team-select/index.tsx\":[\"src/pages/user/team-select/index.tsx\"]});;\r\n  },\r\n);\r\n", "import React, { useState, useEffect } from \"react\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  Card,\r\n  Divider,\r\n  Dropdown,\r\n  Flex,\r\n  Form,\r\n  Input,\r\n  List,\r\n  Modal,\r\n  Progress,\r\n  Select,\r\n  Space,\r\n  Tabs,\r\n  Tag,\r\n  Tooltip,\r\n  Typography,\r\n  Spin,\r\n  Alert,\r\n  message,\r\n} from \"antd\";\r\nimport { TodoService } from \"@/services/todo\";\r\nimport type { TodoResponse, TodoStatsResponse } from \"@/types/api\";\r\nimport {\r\n  CheckOutlined,\r\n  DeleteOutlined,\r\n  EditOutlined,\r\n  MoreOutlined,\r\n  PlusOutlined,\r\n  SearchOutlined,\r\n  CalendarOutlined,\r\n} from \"@ant-design/icons\";\r\n\r\nconst { Text } = Typography;\r\nconst { TabPane } = Tabs;\r\n\r\nexport interface TodoItem {\r\n  id: string;\r\n  name: string;\r\n  createDate: string;\r\n  completed: boolean;\r\n  priority: \"high\" | \"medium\" | \"low\";\r\n}\r\n\r\ninterface TodoManagementProps {\r\n  onAddTodo?: (todo: TodoItem) => void;\r\n  onUpdateTodo?: (id: string, updatedTodo: Partial<TodoItem>) => void;\r\n  onDeleteTodo?: (id: string) => void;\r\n}\r\n\r\nconst TodoManagement: React.FC<TodoManagementProps> = (props) => {\r\n  // 初始待办事项数据\r\n  const [personalTasks, setPersonalTasks] = useState<TodoItem[]>([\r\n    {\r\n      id: \"1\",\r\n      name: \"完善个人资料信息\",\r\n      createDate: \"2024/1/10\",\r\n      completed: true,\r\n      priority: \"low\",\r\n    },\r\n    {\r\n      id: \"2\",\r\n      name: \"驾驶证信息完善\",\r\n      createDate: \"2024/1/12\",\r\n      completed: true,\r\n      priority: \"medium\",\r\n    },\r\n    {\r\n      id: \"3\",\r\n      name: \"设置提醒收藏\",\r\n      createDate: \"2024/1/15\",\r\n      completed: true,\r\n      priority: \"low\",\r\n    },\r\n    {\r\n      id: \"4\",\r\n      name: \"车辆保养记录更新\",\r\n      createDate: \"2024/1/18\",\r\n      completed: false,\r\n      priority: \"medium\",\r\n    },\r\n    {\r\n      id: \"5\",\r\n      name: \"团队会议准备\",\r\n      createDate: \"2024/1/20\",\r\n      completed: false,\r\n      priority: \"high\",\r\n    },\r\n    {\r\n      id: \"6\",\r\n      name: \"车辆安全检查报告\",\r\n      createDate: \"2024/1/22\",\r\n      completed: false,\r\n      priority: \"high\",\r\n    },\r\n  ]);\r\n\r\n  // 待办事项状态管理\r\n  const [todoModalVisible, setTodoModalVisible] = useState(false);\r\n  const [todoForm] = Form.useForm();\r\n  const [editingTodoId, setEditingTodoId] = useState<string | null>(null);\r\n\r\n  // 过滤器状态\r\n  const [activeTab, setActiveTab] = useState<\"all\" | \"pending\" | \"completed\">(\r\n    \"pending\"\r\n  );\r\n  const [searchText, setSearchText] = useState(\"\");\r\n\r\n  // 根据激活的标签和搜索文本过滤任务\r\n  const filteredPersonalTasks = personalTasks.filter((task) => {\r\n    // 根据标签过滤\r\n    if (activeTab === \"pending\" && task.completed) return false;\r\n    if (activeTab === \"completed\" && !task.completed) return false;\r\n\r\n    // 根据搜索文本过滤\r\n    if (\r\n      searchText &&\r\n      !task.name.toLowerCase().includes(searchText.toLowerCase())\r\n    ) {\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  });\r\n\r\n  // 计算各优先级任务数量\r\n  const highPriorityCount = personalTasks.filter(\r\n    (t) => !t.completed && t.priority === \"high\"\r\n  ).length;\r\n  const mediumPriorityCount = personalTasks.filter(\r\n    (t) => !t.completed && t.priority === \"medium\"\r\n  ).length;\r\n  const lowPriorityCount = personalTasks.filter(\r\n    (t) => !t.completed && t.priority === \"low\"\r\n  ).length;\r\n\r\n  // 计算完成百分比\r\n  const completionPercentage = Math.round(\r\n    (personalTasks.filter((task) => task.completed).length /\r\n      personalTasks.length) *\r\n      100\r\n  );\r\n\r\n  // 处理待办事项操作\r\n  const handleToggleTodoStatus = (id: string) => {\r\n    setPersonalTasks(\r\n      personalTasks.map((task) =>\r\n        task.id === id ? { ...task, completed: !task.completed } : task\r\n      )\r\n    );\r\n  };\r\n\r\n  const handleAddOrUpdateTodo = (values: any) => {\r\n    if (editingTodoId) {\r\n      // 更新现有待办事项\r\n      setPersonalTasks(\r\n        personalTasks.map((task) =>\r\n          task.id === editingTodoId\r\n            ? { ...task, name: values.name, priority: values.priority }\r\n            : task\r\n        )\r\n      );\r\n    } else {\r\n      // 添加新待办事项\r\n      const newTodo: TodoItem = {\r\n        id: `${Date.now()}`,\r\n        name: values.name,\r\n        createDate: new Date().toISOString().split(\"T\")[0],\r\n        completed: false,\r\n        priority: values.priority,\r\n      };\r\n      setPersonalTasks([...personalTasks, newTodo]);\r\n    }\r\n\r\n    // 重置表单并关闭模态框\r\n    setTodoModalVisible(false);\r\n    setEditingTodoId(null);\r\n    todoForm.resetFields();\r\n  };\r\n\r\n  const handleDeleteTodo = (id: string) => {\r\n    setPersonalTasks(personalTasks.filter((task) => task.id !== id));\r\n  };\r\n\r\n  return (\r\n    <Card\r\n      className=\"dashboard-card\"\r\n      style={{\r\n        borderRadius: 12,\r\n        boxShadow: \"0 4px 12px rgba(0,0,0,0.05)\",\r\n        border: \"none\",\r\n        background: \"linear-gradient(145deg, #ffffff, #f5f8ff)\",\r\n      }}\r\n      title={\r\n        <Flex justify=\"space-between\" align=\"center\">\r\n          <Text strong>待办事项</Text>\r\n        </Flex>\r\n      }\r\n    >\r\n      {/* 第一行：搜索框、新增任务按钮、优先级状态点和进度条 */}\r\n      <Flex\r\n        justify=\"space-between\"\r\n        align=\"center\"\r\n        style={{ marginBottom: 16, gap: 16, flexWrap: \"wrap\" }}\r\n      >\r\n        <Space size={16} style={{ flex: 1, minWidth: 300 }}>\r\n          {/* 搜索框 */}\r\n          <Input.Search\r\n            placeholder=\"搜索任务...\"\r\n            allowClear\r\n            prefix={<SearchOutlined />}\r\n            value={searchText}\r\n            onChange={(e) => setSearchText(e.target.value)}\r\n            style={{ flex: 1 }}\r\n          />\r\n\r\n          {/* 新增任务按钮 */}\r\n          <Button\r\n            type=\"primary\"\r\n            icon={<PlusOutlined />}\r\n            onClick={() => {\r\n              setEditingTodoId(null);\r\n              todoForm.resetFields();\r\n              setTodoModalVisible(true);\r\n            }}\r\n            style={{\r\n              background: \"#1890ff\",\r\n              borderColor: \"#1890ff\",\r\n              boxShadow: \"0 2px 4px rgba(24, 144, 255, 0.3)\",\r\n              fontWeight: 500,\r\n            }}\r\n          >\r\n            新增\r\n          </Button>\r\n        </Space>\r\n        \r\n        <Divider type=\"vertical\" style={{ height: 20, backgroundColor: \"#e8e8e8\" }} />\r\n        \r\n        <Space align=\"center\" size={16}>\r\n          {/* 优先级状态点 */}\r\n          <Space size={8}>\r\n            <Tooltip title=\"高优先级任务\">\r\n              <Flex align=\"center\">\r\n                <div\r\n                  style={{\r\n                    width: 10,\r\n                    height: 10,\r\n                    borderRadius: \"50%\",\r\n                    background: \"#ff4d4f\",\r\n                    marginRight: 4,\r\n                  }}\r\n                />\r\n                <Text style={{ fontSize: 13, fontWeight: 500 }}>\r\n                  1000\r\n                </Text>\r\n              </Flex>\r\n            </Tooltip>\r\n\r\n            <Tooltip title=\"中优先级任务\">\r\n              <Flex align=\"center\">\r\n                <div\r\n                  style={{\r\n                    width: 10,\r\n                    height: 10,\r\n                    borderRadius: \"50%\",\r\n                    background: \"#faad14\",\r\n                    marginRight: 4,\r\n                  }}\r\n                />\r\n                <Text style={{ fontSize: 13, fontWeight: 500 }}>\r\n                  10000\r\n                </Text>\r\n              </Flex>\r\n            </Tooltip>\r\n\r\n            <Tooltip title=\"低优先级任务\">\r\n              <Flex align=\"center\">\r\n                <div\r\n                  style={{\r\n                    width: 10,\r\n                    height: 10,\r\n                    borderRadius: \"50%\",\r\n                    background: \"#52c41a\",\r\n                    marginRight: 4,\r\n                  }}\r\n                />\r\n                <Text style={{ fontSize: 13, fontWeight: 500 }}>\r\n                  10000\r\n                </Text>\r\n              </Flex>\r\n            </Tooltip>\r\n          </Space>\r\n\r\n          <Divider type=\"vertical\" style={{ height: 20, backgroundColor: \"#e8e8e8\" }} />\r\n\r\n          {/* 进度条 */}\r\n          <Tooltip title=\"任务完成进度\">\r\n            <Progress\r\n              percent={completionPercentage}\r\n              size=\"small\"\r\n              style={{ width: 120 }}\r\n              strokeColor=\"#52c41a\"\r\n            />\r\n          </Tooltip>\r\n        </Space>\r\n      </Flex>\r\n\r\n      {/* 第二行：标签页 */}\r\n      <Tabs\r\n        activeKey={activeTab}\r\n        onChange={(key) =>\r\n          setActiveTab(key as \"all\" | \"pending\" | \"completed\")\r\n        }\r\n        size=\"middle\"\r\n        style={{ marginBottom: 8 }}\r\n      >\r\n        <TabPane tab=\"全部\" key=\"all\" />\r\n        <TabPane tab=\"待处理\" key=\"pending\" />\r\n        <TabPane tab=\"已完成\" key=\"completed\" />\r\n      </Tabs>\r\n\r\n      {/* 待办事项列表 */}\r\n      <List\r\n        dataSource={filteredPersonalTasks}\r\n        renderItem={(item) => {\r\n          return (\r\n            <List.Item\r\n              className=\"todo-item\"\r\n              style={{\r\n                padding: \"10px 16px\",\r\n                marginBottom: 12,\r\n                borderRadius: 8,\r\n                background: \"#fff\",\r\n                opacity: item.completed ? 0.7 : 1,\r\n                borderLeft: `3px solid ${\r\n                  item.completed\r\n                    ? \"#52c41a\"\r\n                    : item.priority === \"high\"\r\n                    ? \"#ff4d4f\"\r\n                    : item.priority === \"medium\"\r\n                    ? \"#faad14\"\r\n                    : \"#8c8c8c\"\r\n                }`,\r\n                boxShadow: \"0 1px 4px rgba(0,0,0,0.05)\",\r\n              }}\r\n            >\r\n              <Flex align=\"center\" gap={12} style={{ width: \"100%\" }}>\r\n                {/* 左侧状态和优先级指示器 */}\r\n                <Flex vertical align=\"center\">\r\n                  {item.completed ? (\r\n                    <Flex\r\n                      align=\"center\"\r\n                      justify=\"center\"\r\n                      style={{\r\n                        width: 22,\r\n                        height: 22,\r\n                        borderRadius: \"50%\",\r\n                        background: \"#52c41a\",\r\n                      }}\r\n                    >\r\n                      <CheckOutlined\r\n                        style={{ color: \"#fff\", fontSize: 12 }}\r\n                      />\r\n                    </Flex>\r\n                  ) : (\r\n                    <div\r\n                      style={{\r\n                        width: 18,\r\n                        height: 18,\r\n                        borderRadius: \"50%\",\r\n                        border: `2px solid ${\r\n                          item.priority === \"high\"\r\n                            ? \"#ff4d4f\"\r\n                            : item.priority === \"medium\"\r\n                            ? \"#faad14\"\r\n                            : \"#8c8c8c\"\r\n                        }`,\r\n                      }}\r\n                    />\r\n                  )}\r\n\r\n                  <div\r\n                    style={{\r\n                      width: 2,\r\n                      height: 24,\r\n                      background: \"#f0f0f0\",\r\n                      marginTop: 4,\r\n                    }}\r\n                  />\r\n                </Flex>\r\n\r\n                {/* 任务信息区 */}\r\n                <Flex vertical style={{ flex: 1 }}>\r\n                  <Text\r\n                    style={{\r\n                      fontSize: 14,\r\n                      fontWeight:\r\n                        item.priority === \"high\" ? 500 : \"normal\",\r\n                      textDecoration: item.completed\r\n                        ? \"line-through\"\r\n                        : \"none\",\r\n                      color: item.completed ? \"#8c8c8c\" : \"#262626\",\r\n                    }}\r\n                  >\r\n                    {item.name}\r\n                  </Text>\r\n\r\n                  {/* 显示创建日期 */}\r\n                  <Space align=\"center\" size={6} style={{ marginTop: 4 }}>\r\n                    <CalendarOutlined\r\n                      style={{\r\n                        fontSize: 12,\r\n                        color: \"#8c8c8c\",\r\n                      }}\r\n                    />\r\n                    <Text type=\"secondary\" style={{ fontSize: 12 }}>\r\n                      创建于: {item.createDate}\r\n                    </Text>\r\n                  </Space>\r\n                </Flex>\r\n\r\n                {/* 操作按钮区 */}\r\n                <Dropdown\r\n                  trigger={['click']}\r\n                  menu={{\r\n                    items: [\r\n                      {\r\n                        key: 'complete',\r\n                        label: item.completed ? '标记未完成' : '标记完成',\r\n                        icon: (\r\n                          <CheckOutlined \r\n                            style={{ \r\n                              color: item.completed ? '#8c8c8c' : '#52c41a',\r\n                              fontSize: 14 \r\n                            }} \r\n                          />\r\n                        )\r\n                      },\r\n                      {\r\n                        key: 'edit',\r\n                        label: '编辑任务',\r\n                        icon: <EditOutlined style={{ color: '#8c8c8c' }} />\r\n                      },\r\n                      {\r\n                        key: 'delete',\r\n                        label: '删除任务',\r\n                        icon: <DeleteOutlined style={{ color: '#ff4d4f' }} />,\r\n                        danger: true\r\n                      }\r\n                    ],\r\n                    onClick: ({ key }) => {\r\n                      if (key === \"complete\") {\r\n                        handleToggleTodoStatus(item.id);\r\n                      } else if (key === \"edit\") {\r\n                        setEditingTodoId(item.id);\r\n                        todoForm.setFieldsValue({\r\n                          name: item.name,\r\n                          priority: item.priority\r\n                        });\r\n                        setTodoModalVisible(true);\r\n                      } else if (key === \"delete\") {\r\n                        handleDeleteTodo(item.id);\r\n                      }\r\n                    }\r\n                  }}\r\n                >\r\n                  <Button \r\n                    type=\"text\" \r\n                    size=\"small\" \r\n                    icon={<MoreOutlined />} \r\n                    style={{ width: 32, height: 32 }} \r\n                  />\r\n                </Dropdown>\r\n              </Flex>\r\n            </List.Item>\r\n          );\r\n        }}\r\n      />\r\n\r\n      {/* 待办事项表单模态框 */}\r\n      <Modal\r\n        title={editingTodoId ? \"编辑待办事项\" : \"新增待办事项\"}\r\n        open={todoModalVisible}\r\n        onCancel={() => {\r\n          setTodoModalVisible(false);\r\n          todoForm.resetFields();\r\n        }}\r\n        onOk={() => {\r\n          todoForm.submit();\r\n        }}\r\n        centered\r\n        destroyOnClose\r\n        footer={[\r\n          <Button key=\"cancel\" onClick={() => setTodoModalVisible(false)}>\r\n            取消\r\n          </Button>,\r\n          <Button\r\n            key=\"submit\"\r\n            type=\"primary\"\r\n            onClick={() => {\r\n              todoForm.submit();\r\n            }}\r\n            style={{\r\n              background: \"#1890ff\",\r\n              borderColor: \"#1890ff\",\r\n              boxShadow: \"0 2px 4px rgba(24, 144, 255, 0.3)\",\r\n            }}\r\n          >\r\n            {editingTodoId ? \"更新任务\" : \"创建任务\"}\r\n          </Button>,\r\n        ]}\r\n      >\r\n        <Form\r\n          form={todoForm}\r\n          layout=\"vertical\"\r\n          onFinish={handleAddOrUpdateTodo}\r\n          autoComplete=\"off\"\r\n        >\r\n          <Form.Item\r\n            name=\"name\"\r\n            label=\"任务名称\"\r\n            rules={[{ required: true, message: \"请输入任务名称\" }]}\r\n          >\r\n            <Input\r\n              placeholder=\"请输入任务名称\"\r\n              size=\"large\"\r\n              style={{ borderRadius: 6 }}\r\n            />\r\n          </Form.Item>\r\n\r\n          <Form.Item\r\n            name=\"priority\"\r\n            label=\"优先级\"\r\n            initialValue=\"medium\"\r\n            rules={[{ required: true, message: \"请选择优先级\" }]}\r\n          >\r\n            <Select\r\n              size=\"large\"\r\n              options={[\r\n                { value: \"high\", label: \"高优先级\" },\r\n                { value: \"medium\", label: \"中优先级\" },\r\n                { value: \"low\", label: \"低优先级\" },\r\n              ]}\r\n              style={{ borderRadius: 6 }}\r\n            />\r\n          </Form.Item>\r\n        </Form>\r\n      </Modal>\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport default TodoManagement;"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,iDACA;IACE,SAAS;;;;;;wCCsiBb;;;2BAAA;;;;;;oFAziB2C;yCAqBpC;0CAWA;;;;;;;;;;YAEP,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAU;YAC3B,MAAM,EAAE,OAAO,EAAE,GAAG,UAAI;YAgBxB,MAAM,iBAAgD,CAAC;;gBACrD,WAAW;gBACX,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAa;oBAC7D;wBACE,IAAI;wBACJ,MAAM;wBACN,YAAY;wBACZ,WAAW;wBACX,UAAU;oBACZ;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,YAAY;wBACZ,WAAW;wBACX,UAAU;oBACZ;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,YAAY;wBACZ,WAAW;wBACX,UAAU;oBACZ;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,YAAY;wBACZ,WAAW;wBACX,UAAU;oBACZ;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,YAAY;wBACZ,WAAW;wBACX,UAAU;oBACZ;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,YAAY;wBACZ,WAAW;wBACX,UAAU;oBACZ;iBACD;gBAED,WAAW;gBACX,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,eAAQ,EAAC;gBACzD,MAAM,CAAC,SAAS,GAAG,UAAI,CAAC,OAAO;gBAC/B,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAgB;gBAElE,QAAQ;gBACR,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EACxC;gBAEF,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAC;gBAE7C,mBAAmB;gBACnB,MAAM,wBAAwB,cAAc,MAAM,CAAC,CAAC;oBAClD,SAAS;oBACT,IAAI,cAAc,aAAa,KAAK,SAAS,EAAE,OAAO;oBACtD,IAAI,cAAc,eAAe,CAAC,KAAK,SAAS,EAAE,OAAO;oBAEzD,WAAW;oBACX,IACE,cACA,CAAC,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,KAExD,OAAO;oBAGT,OAAO;gBACT;gBAEA,aAAa;gBACa,cAAc,MAAM,CAC5C,CAAC,IAAM,CAAC,EAAE,SAAS,IAAI,EAAE,QAAQ,KAAK,QACtC,MAAM;gBACoB,cAAc,MAAM,CAC9C,CAAC,IAAM,CAAC,EAAE,SAAS,IAAI,EAAE,QAAQ,KAAK,UACtC,MAAM;gBACiB,cAAc,MAAM,CAC3C,CAAC,IAAM,CAAC,EAAE,SAAS,IAAI,EAAE,QAAQ,KAAK,OACtC,MAAM;gBAER,UAAU;gBACV,MAAM,uBAAuB,KAAK,KAAK,CACrC,AAAC,cAAc,MAAM,CAAC,CAAC,OAAS,KAAK,SAAS,EAAE,MAAM,GACpD,cAAc,MAAM,GACpB;gBAGJ,WAAW;gBACX,MAAM,yBAAyB,CAAC;oBAC9B,iBACE,cAAc,GAAG,CAAC,CAAC,OACjB,KAAK,EAAE,KAAK,KAAK;4BAAE,GAAG,IAAI;4BAAE,WAAW,CAAC,KAAK,SAAS;wBAAC,IAAI;gBAGjE;gBAEA,MAAM,wBAAwB,CAAC;oBAC7B,IAAI,eACF,WAAW;oBACX,iBACE,cAAc,GAAG,CAAC,CAAC,OACjB,KAAK,EAAE,KAAK,gBACR;4BAAE,GAAG,IAAI;4BAAE,MAAM,OAAO,IAAI;4BAAE,UAAU,OAAO,QAAQ;wBAAC,IACxD;yBAGH;wBACL,UAAU;wBACV,MAAM,UAAoB;4BACxB,IAAI,CAAC,EAAE,KAAK,GAAG,GAAG,CAAC;4BACnB,MAAM,OAAO,IAAI;4BACjB,YAAY,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;4BAClD,WAAW;4BACX,UAAU,OAAO,QAAQ;wBAC3B;wBACA,iBAAiB;+BAAI;4BAAe;yBAAQ;oBAC9C;oBAEA,aAAa;oBACb,oBAAoB;oBACpB,iBAAiB;oBACjB,SAAS,WAAW;gBACtB;gBAEA,MAAM,mBAAmB,CAAC;oBACxB,iBAAiB,cAAc,MAAM,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;gBAC9D;gBAEA,qBACE,2BAAC,UAAI;oBACH,WAAU;oBACV,OAAO;wBACL,cAAc;wBACd,WAAW;wBACX,QAAQ;wBACR,YAAY;oBACd;oBACA,qBACE,2BAAC,UAAI;wBAAC,SAAQ;wBAAgB,OAAM;kCAClC,cAAA,2BAAC;4BAAK,MAAM;sCAAC;;;;;;;;;;;;sCAKjB,2BAAC,UAAI;4BACH,SAAQ;4BACR,OAAM;4BACN,OAAO;gCAAE,cAAc;gCAAI,KAAK;gCAAI,UAAU;4BAAO;;8CAErD,2BAAC,WAAK;oCAAC,MAAM;oCAAI,OAAO;wCAAE,MAAM;wCAAG,UAAU;oCAAI;;sDAE/C,2BAAC,WAAK,CAAC,MAAM;4CACX,aAAY;4CACZ,UAAU;4CACV,sBAAQ,2BAAC,qBAAc;;;;;4CACvB,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,OAAO;gDAAE,MAAM;4CAAE;;;;;;sDAInB,2BAAC,YAAM;4CACL,MAAK;4CACL,oBAAM,2BAAC,mBAAY;;;;;4CACnB,SAAS;gDACP,iBAAiB;gDACjB,SAAS,WAAW;gDACpB,oBAAoB;4CACtB;4CACA,OAAO;gDACL,YAAY;gDACZ,aAAa;gDACb,WAAW;gDACX,YAAY;4CACd;sDACD;;;;;;;;;;;;8CAKH,2BAAC,aAAO;oCAAC,MAAK;oCAAW,OAAO;wCAAE,QAAQ;wCAAI,iBAAiB;oCAAU;;;;;;8CAEzE,2BAAC,WAAK;oCAAC,OAAM;oCAAS,MAAM;;sDAE1B,2BAAC,WAAK;4CAAC,MAAM;;8DACX,2BAAC,aAAO;oDAAC,OAAM;8DACb,cAAA,2BAAC,UAAI;wDAAC,OAAM;;0EACV,2BAAC;gEACC,OAAO;oEACL,OAAO;oEACP,QAAQ;oEACR,cAAc;oEACd,YAAY;oEACZ,aAAa;gEACf;;;;;;0EAEF,2BAAC;gEAAK,OAAO;oEAAE,UAAU;oEAAI,YAAY;gEAAI;0EAAG;;;;;;;;;;;;;;;;;8DAMpD,2BAAC,aAAO;oDAAC,OAAM;8DACb,cAAA,2BAAC,UAAI;wDAAC,OAAM;;0EACV,2BAAC;gEACC,OAAO;oEACL,OAAO;oEACP,QAAQ;oEACR,cAAc;oEACd,YAAY;oEACZ,aAAa;gEACf;;;;;;0EAEF,2BAAC;gEAAK,OAAO;oEAAE,UAAU;oEAAI,YAAY;gEAAI;0EAAG;;;;;;;;;;;;;;;;;8DAMpD,2BAAC,aAAO;oDAAC,OAAM;8DACb,cAAA,2BAAC,UAAI;wDAAC,OAAM;;0EACV,2BAAC;gEACC,OAAO;oEACL,OAAO;oEACP,QAAQ;oEACR,cAAc;oEACd,YAAY;oEACZ,aAAa;gEACf;;;;;;0EAEF,2BAAC;gEAAK,OAAO;oEAAE,UAAU;oEAAI,YAAY;gEAAI;0EAAG;;;;;;;;;;;;;;;;;;;;;;;sDAOtD,2BAAC,aAAO;4CAAC,MAAK;4CAAW,OAAO;gDAAE,QAAQ;gDAAI,iBAAiB;4CAAU;;;;;;sDAGzE,2BAAC,aAAO;4CAAC,OAAM;sDACb,cAAA,2BAAC,cAAQ;gDACP,SAAS;gDACT,MAAK;gDACL,OAAO;oDAAE,OAAO;gDAAI;gDACpB,aAAY;;;;;;;;;;;;;;;;;;;;;;;sCAOpB,2BAAC,UAAI;4BACH,WAAW;4BACX,UAAU,CAAC,MACT,aAAa;4BAEf,MAAK;4BACL,OAAO;gCAAE,cAAc;4BAAE;;8CAEzB,2BAAC;oCAAQ,KAAI;mCAAS;;;;;8CACtB,2BAAC;oCAAQ,KAAI;mCAAU;;;;;8CACvB,2BAAC;oCAAQ,KAAI;mCAAU;;;;;;;;;;;sCAIzB,2BAAC,UAAI;4BACH,YAAY;4BACZ,YAAY,CAAC;gCACX,qBACE,2BAAC,UAAI,CAAC,IAAI;oCACR,WAAU;oCACV,OAAO;wCACL,SAAS;wCACT,cAAc;wCACd,cAAc;wCACd,YAAY;wCACZ,SAAS,KAAK,SAAS,GAAG,MAAM;wCAChC,YAAY,CAAC,UAAU,EACrB,KAAK,SAAS,GACV,YACA,KAAK,QAAQ,KAAK,SAClB,YACA,KAAK,QAAQ,KAAK,WAClB,YACA,UACL,CAAC;wCACF,WAAW;oCACb;8CAEA,cAAA,2BAAC,UAAI;wCAAC,OAAM;wCAAS,KAAK;wCAAI,OAAO;4CAAE,OAAO;wCAAO;;0DAEnD,2BAAC,UAAI;gDAAC,QAAQ;gDAAC,OAAM;;oDAClB,KAAK,SAAS,iBACb,2BAAC,UAAI;wDACH,OAAM;wDACN,SAAQ;wDACR,OAAO;4DACL,OAAO;4DACP,QAAQ;4DACR,cAAc;4DACd,YAAY;wDACd;kEAEA,cAAA,2BAAC,oBAAa;4DACZ,OAAO;gEAAE,OAAO;gEAAQ,UAAU;4DAAG;;;;;;;;;;+EAIzC,2BAAC;wDACC,OAAO;4DACL,OAAO;4DACP,QAAQ;4DACR,cAAc;4DACd,QAAQ,CAAC,UAAU,EACjB,KAAK,QAAQ,KAAK,SACd,YACA,KAAK,QAAQ,KAAK,WAClB,YACA,UACL,CAAC;wDACJ;;;;;;kEAIJ,2BAAC;wDACC,OAAO;4DACL,OAAO;4DACP,QAAQ;4DACR,YAAY;4DACZ,WAAW;wDACb;;;;;;;;;;;;0DAKJ,2BAAC,UAAI;gDAAC,QAAQ;gDAAC,OAAO;oDAAE,MAAM;gDAAE;;kEAC9B,2BAAC;wDACC,OAAO;4DACL,UAAU;4DACV,YACE,KAAK,QAAQ,KAAK,SAAS,MAAM;4DACnC,gBAAgB,KAAK,SAAS,GAC1B,iBACA;4DACJ,OAAO,KAAK,SAAS,GAAG,YAAY;wDACtC;kEAEC,KAAK,IAAI;;;;;;kEAIZ,2BAAC,WAAK;wDAAC,OAAM;wDAAS,MAAM;wDAAG,OAAO;4DAAE,WAAW;wDAAE;;0EACnD,2BAAC,uBAAgB;gEACf,OAAO;oEACL,UAAU;oEACV,OAAO;gEACT;;;;;;0EAEF,2BAAC;gEAAK,MAAK;gEAAY,OAAO;oEAAE,UAAU;gEAAG;;oEAAG;oEACxC,KAAK,UAAU;;;;;;;;;;;;;;;;;;;0DAM3B,2BAAC,cAAQ;gDACP,SAAS;oDAAC;iDAAQ;gDAClB,MAAM;oDACJ,OAAO;wDACL;4DACE,KAAK;4DACL,OAAO,KAAK,SAAS,GAAG,UAAU;4DAClC,oBACE,2BAAC,oBAAa;gEACZ,OAAO;oEACL,OAAO,KAAK,SAAS,GAAG,YAAY;oEACpC,UAAU;gEACZ;;;;;;wDAGN;wDACA;4DACE,KAAK;4DACL,OAAO;4DACP,oBAAM,2BAAC,mBAAY;gEAAC,OAAO;oEAAE,OAAO;gEAAU;;;;;;wDAChD;wDACA;4DACE,KAAK;4DACL,OAAO;4DACP,oBAAM,2BAAC,qBAAc;gEAAC,OAAO;oEAAE,OAAO;gEAAU;;;;;;4DAChD,QAAQ;wDACV;qDACD;oDACD,SAAS,CAAC,EAAE,GAAG,EAAE;wDACf,IAAI,QAAQ,YACV,uBAAuB,KAAK,EAAE;6DACzB,IAAI,QAAQ,QAAQ;4DACzB,iBAAiB,KAAK,EAAE;4DACxB,SAAS,cAAc,CAAC;gEACtB,MAAM,KAAK,IAAI;gEACf,UAAU,KAAK,QAAQ;4DACzB;4DACA,oBAAoB;wDACtB,OAAO,IAAI,QAAQ,UACjB,iBAAiB,KAAK,EAAE;oDAE5B;gDACF;0DAEA,cAAA,2BAAC,YAAM;oDACL,MAAK;oDACL,MAAK;oDACL,oBAAM,2BAAC,mBAAY;;;;;oDACnB,OAAO;wDAAE,OAAO;wDAAI,QAAQ;oDAAG;;;;;;;;;;;;;;;;;;;;;;4BAM3C;;;;;;sCAIF,2BAAC,WAAK;4BACJ,OAAO,gBAAgB,WAAW;4BAClC,MAAM;4BACN,UAAU;gCACR,oBAAoB;gCACpB,SAAS,WAAW;4BACtB;4BACA,MAAM;gCACJ,SAAS,MAAM;4BACjB;4BACA,QAAQ;4BACR,cAAc;4BACd,QAAQ;8CACN,2BAAC,YAAM;oCAAc,SAAS,IAAM,oBAAoB;8CAAQ;mCAApD;;;;;8CAGZ,2BAAC,YAAM;oCAEL,MAAK;oCACL,SAAS;wCACP,SAAS,MAAM;oCACjB;oCACA,OAAO;wCACL,YAAY;wCACZ,aAAa;wCACb,WAAW;oCACb;8CAEC,gBAAgB,SAAS;mCAXtB;;;;;6BAaP;sCAED,cAAA,2BAAC,UAAI;gCACH,MAAM;gCACN,QAAO;gCACP,UAAU;gCACV,cAAa;;kDAEb,2BAAC,UAAI,CAAC,IAAI;wCACR,MAAK;wCACL,OAAM;wCACN,OAAO;4CAAC;gDAAE,UAAU;gDAAM,SAAS;4CAAU;yCAAE;kDAE/C,cAAA,2BAAC,WAAK;4CACJ,aAAY;4CACZ,MAAK;4CACL,OAAO;gDAAE,cAAc;4CAAE;;;;;;;;;;;kDAI7B,2BAAC,UAAI,CAAC,IAAI;wCACR,MAAK;wCACL,OAAM;wCACN,cAAa;wCACb,OAAO;4CAAC;gDAAE,UAAU;gDAAM,SAAS;4CAAS;yCAAE;kDAE9C,cAAA,2BAAC,YAAM;4CACL,MAAK;4CACL,SAAS;gDACP;oDAAE,OAAO;oDAAQ,OAAO;gDAAO;gDAC/B;oDAAE,OAAO;oDAAU,OAAO;gDAAO;gDACjC;oDAAE,OAAO;oDAAO,OAAO;gDAAO;6CAC/B;4CACD,OAAO;gDAAE,cAAc;4CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOvC;eApfM;;oBAiDe,UAAI,CAAC;;;iBAjDpB;gBAsfN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;IDtiBD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,uCAAsC;YAAC;YAAU;SAAsC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,8BAA6B;YAAC;SAAmB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;YAAS;SAAyB;QAAC,mCAAkC;YAAC;YAAS;SAAyB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,iDAAgD;YAAC;SAAgD;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,kCAAiC;YAAC;SAAwB;QAAC,wCAAuC;YAAC;SAAuC;IAAA;;AACh/B"}