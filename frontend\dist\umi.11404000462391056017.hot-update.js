globalThis.makoModuleHotUpdate('src/.umi/umi.ts?hmr', {
    modules: {
        "src/services/user.ts": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            function _export(target, all) {
                for(var name in all)Object.defineProperty(target, name, {
                    enumerable: true,
                    get: all[name]
                });
            }
            __mako_require__.e(exports, {
                UserService: function() {
                    return UserService;
                },
                // 导出默认实例
                default: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _request = __mako_require__("src/utils/request.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            class UserService {
                /**
   * 获取当前用户资料
   */ static async getUserProfile() {
                    const response = await _request.apiRequest.get('/users/profile');
                    return response.data;
                }
                /**
   * 更新用户资料
   */ static async updateUserProfile(data) {
                    const response = await _request.apiRequest.put('/users/profile', data);
                    return response.data;
                }
                /**
   * 修改密码
   */ static async changePassword(currentPassword, newPassword) {
                    const data = {
                        currentPassword,
                        newPassword
                    };
                    const response = await _request.apiRequest.put('/users/profile', data);
                    return response.data;
                }
                /**
   * 更新用户名
   */ static async updateUserName(name) {
                    const data = {
                        name
                    };
                    const response = await _request.apiRequest.put('/users/profile', data);
                    return response.data;
                }
                /**
   * 验证当前密码
   */ static async validateCurrentPassword(password) {
                    try {
                        const response = await _request.apiRequest.post('/users/validate-password', {
                            password
                        });
                        return response.data;
                    } catch  {
                        return false;
                    }
                }
                /**
   * 获取用户统计信息
   */ static async getUserStats() {
                    // 这里可能需要后端提供专门的统计接口
                    // 暂时返回模拟数据
                    return {
                        totalTeams: 0,
                        createdTeams: 0,
                        joinedTeams: 0,
                        lastLoginTime: new Date().toISOString()
                    };
                }
                /**
   * 获取用户个人统计数据（车辆、人员、预警、告警）
   */ static async getUserPersonalStats() {
                    const response = await _request.apiRequest.get('/users/personal-stats');
                    return response.data;
                }
                /**
   * 检查邮箱是否已被使用
   */ static async checkEmailAvailable(email) {
                    try {
                        // 这里可能需要后端提供专门的检查接口
                        // 暂时返回 true
                        return true;
                    } catch  {
                        return false;
                    }
                }
                /**
   * 获取用户活动日志
   */ static async getUserActivityLog(params) {
                    // 这里需要后端提供活动日志接口
                    // 暂时返回空数组
                    return [];
                }
                /**
   * 导出用户数据
   */ static async exportUserData() {
                    // 这里需要后端提供数据导出接口
                    const response = await _request.apiRequest.get('/users/export');
                    return response;
                }
                /**
   * 删除用户账户
   */ static async deleteAccount(password) {
                    const response = await _request.apiRequest.delete('/users/account', {
                        password
                    });
                    return response.data;
                }
            }
            var _default = UserService;
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '17712021398501579483';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/.umi/plugin-openapi/openapi.tsx": [
            "vendors",
            "src/.umi/plugin-openapi/openapi.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/friend/index.tsx": [
            "p__friend__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "common",
            "p__subscription__index"
        ],
        "src/pages/team/detail/index.tsx": [
            "common",
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "p__team__index"
        ],
        "src/pages/test/profile-consolidated/index.tsx": [
            "src/pages/test/profile-consolidated/index.tsx"
        ],
        "src/pages/user/index.tsx": [
            "common",
            "p__user__index"
        ],
        "src/pages/user/login/index.tsx": [
            "p__user__login__index"
        ],
        "src/pages/user/team-select/index.tsx": [
            "src/pages/user/team-select/index.tsx"
        ]
    });
    ;
});

//# sourceMappingURL=umi.11404000462391056017.hot-update.js.map