// @ts-nocheck
// This file is generated by Umi automatically
// DO NOT CHANGE IT MANUALLY!
import DashboardOutlined from 'H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/plugins/node_modules/@ant-design/icons/es/icons/DashboardOutlined';
import TeamOutlined from 'H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/plugins/node_modules/@ant-design/icons/es/icons/TeamOutlined';
import UserOutlined from 'H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/plugins/node_modules/@ant-design/icons/es/icons/UserOutlined';
import ExperimentOutlined from 'H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/plugins/node_modules/@ant-design/icons/es/icons/ExperimentOutlined';
import CrownOutlined from 'H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/plugins/node_modules/@ant-design/icons/es/icons/CrownOutlined';
import UserAddOutlined from 'H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/plugins/node_modules/@ant-design/icons/es/icons/UserAddOutlined';
import QuestionOutlined from 'H:/projects/IdeaProjects/teamAuth/frontend/node_modules/@umijs/plugins/node_modules/@ant-design/icons/es/icons/QuestionOutlined';
export default { DashboardOutlined, TeamOutlined, UserOutlined, ExperimentOutlined, CrownOutlined, UserAddOutlined, QuestionOutlined };
