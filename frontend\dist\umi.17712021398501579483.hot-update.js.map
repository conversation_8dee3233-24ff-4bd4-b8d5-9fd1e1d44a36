{"version": 3, "sources": ["umi.17712021398501579483.hot-update.js", "src/services/team.ts"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/.umi/umi.ts?hmr',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='6772285385776656626';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/.umi/plugin-openapi/openapi.tsx\":[\"vendors\",\"src/.umi/plugin-openapi/openapi.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/friend/index.tsx\":[\"p__friend__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"common\",\"p__subscription__index\"],\"src/pages/team/detail/index.tsx\":[\"common\",\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"p__team__index\"],\"src/pages/test/profile-consolidated/index.tsx\":[\"src/pages/test/profile-consolidated/index.tsx\"],\"src/pages/user/index.tsx\":[\"common\",\"p__user__index\"],\"src/pages/user/login/index.tsx\":[\"p__user__login__index\"],\"src/pages/user/team-select/index.tsx\":[\"src/pages/user/team-select/index.tsx\"]});;\r\n  },\r\n);\r\n", "/**\n * 团队管理相关 API 服务\n */\n\nimport { apiRequest } from '@/utils/request';\nimport type {\n  CreateTeamRequest,\n  UpdateTeamRequest,\n  InviteMembersRequest,\n  TeamDetailResponse,\n  TeamMemberResponse,\n  PageRequest,\n  PageResponse,\n  TeamStatsData,\n} from '@/types/api';\n\n/**\n * 团队服务类\n */\nexport class TeamService {\n  /**\n   * 创建团队（需要 Account Token）\n   */\n  static async createTeam(data: CreateTeamRequest): Promise<TeamDetailResponse> {\n    const response = await apiRequest.post<TeamDetailResponse>('/teams', data);\n    return response.data;\n  }\n\n  /**\n   * 获取用户的团队列表（需要 Account Token）\n   */\n  static async getUserTeams(): Promise<TeamDetailResponse[]> {\n    const response = await apiRequest.get<TeamDetailResponse[]>('/teams');\n    return response.data;\n  }\n\n  /**\n   * 获取用户的团队列表（包含统计数据）\n   */\n  static async getUserTeamsWithStats(): Promise<TeamDetailResponse[]> {\n    const response = await apiRequest.get<TeamDetailResponse[]>('/teams?includeStats=true');\n    return response.data;\n  }\n\n  /**\n   * 获取当前团队详情（需要 Team Token）\n   */\n  static async getCurrentTeamDetail(): Promise<TeamDetailResponse> {\n    const response = await apiRequest.get<TeamDetailResponse>('/teams/current');\n    return response.data;\n  }\n\n  /**\n   * 更新当前团队信息（需要 Team Token，仅创建者）\n   */\n  static async updateCurrentTeam(data: UpdateTeamRequest): Promise<TeamDetailResponse> {\n    const response = await apiRequest.put<TeamDetailResponse>('/teams/current', data);\n    return response.data;\n  }\n\n  /**\n   * 删除当前团队（需要 Team Token，仅创建者）\n   *\n   * 权限要求：\n   * - 需要有效的Team Token\n   * - 只有团队创建者可以执行此操作\n   *\n   * 删除效果：\n   * - 软删除团队记录\n   * - 级联删除所有团队成员关系\n   * - 不可恢复\n   *\n   * @returns Promise<void> 删除成功时resolve\n   * @throws 当权限不足或团队不存在时抛出异常\n   */\n  static async deleteCurrentTeam(): Promise<void> {\n    await apiRequest.delete<string>('/teams/current');\n  }\n\n  /**\n   * 获取当前团队成员列表（需要 Team Token）\n   */\n  static async getTeamMembers(params?: PageRequest): Promise<PageResponse<TeamMemberResponse>> {\n    const response = await apiRequest.get<PageResponse<TeamMemberResponse>>(\n      '/teams/current/members',\n      params\n    );\n    return response.data;\n  }\n\n  /**\n   * 邀请团队成员（需要 Team Token，仅创建者）\n   */\n  static async inviteMembers(data: InviteMembersRequest): Promise<void> {\n    const response = await apiRequest.post<void>('/teams/current/members/invite', data);\n    return response.data;\n  }\n\n  /**\n   * 移除团队成员（需要 Team Token，仅创建者）\n   */\n  static async removeMember(memberId: number): Promise<void> {\n    const response = await apiRequest.delete<void>(`/teams/current/members/${memberId}`);\n    return response.data;\n  }\n\n  /**\n   * 检查团队名称是否可用\n   */\n  static async checkTeamNameAvailable(name: string): Promise<boolean> {\n    try {\n      // 这里可能需要后端提供专门的检查接口\n      // 暂时通过创建团队的错误响应来判断\n      return true;\n    } catch {\n      return false;\n    }\n  }\n\n  /**\n   * 获取团队统计信息\n   */\n  static async getTeamStats(): Promise<{\n    memberCount: number;\n    activeMembers: number;\n    recentActivity: number;\n  }> {\n    // 这里可能需要后端提供专门的统计接口\n    // 暂时通过团队详情和成员列表来计算\n    const teamDetail = await this.getCurrentTeamDetail();\n    const members = await this.getTeamMembers({ current: 1, pageSize: 1000 });\n    \n    const activeMembers = members.list.filter(member => member.isActive).length;\n    const recentActivity = members.list.filter(member => {\n      const lastAccess = new Date(member.lastAccessTime);\n      const weekAgo = new Date();\n      weekAgo.setDate(weekAgo.getDate() - 7);\n      return lastAccess > weekAgo;\n    }).length;\n\n    return {\n      memberCount: teamDetail.memberCount,\n      activeMembers,\n      recentActivity,\n    };\n  }\n\n  /**\n   * 搜索团队成员\n   */\n  static async searchMembers(keyword: string): Promise<TeamMemberResponse[]> {\n    const members = await this.getTeamMembers({ current: 1, pageSize: 1000 });\n    \n    // 前端过滤，后端可以提供专门的搜索接口\n    return members.list.filter(member => \n      member.name.toLowerCase().includes(keyword.toLowerCase()) ||\n      member.email.toLowerCase().includes(keyword.toLowerCase())\n    );\n  }\n\n  /**\n   * 批量操作成员状态\n   */\n  static async batchUpdateMemberStatus(\n    memberIds: number[],\n    isActive: boolean\n  ): Promise<void> {\n    // 这里需要后端提供批量操作接口\n    // 暂时使用循环调用单个接口\n    const promises = memberIds.map(async (memberId) => {\n      // 假设有单个更新成员状态的接口\n      // await this.updateMemberStatus(memberId, isActive);\n    });\n    \n    await Promise.all(promises);\n  }\n}\n\n// 导出默认实例\nexport default TeamService;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uBACA;IACE,SAAS;;;;;;;;;;;;;gBCgBA,WAAW;2BAAX;;gBA+Jb,SAAS;gBACT,OAA2B;2BAA3B;;;;;4CA/K2B;;;;;;;;;YAepB,MAAM;gBACX;;GAEC,GACD,aAAa,WAAW,IAAuB,EAA+B;oBAC5E,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CAAqB,UAAU;oBACrE,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,eAA8C;oBACzD,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAuB;oBAC5D,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,wBAAuD;oBAClE,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAuB;oBAC5D,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,uBAAoD;oBAC/D,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAqB;oBAC1D,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,kBAAkB,IAAuB,EAA+B;oBACnF,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAqB,kBAAkB;oBAC5E,OAAO,SAAS,IAAI;gBACtB;gBAEA;;;;;;;;;;;;;;GAcC,GACD,aAAa,oBAAmC;oBAC9C,MAAM,mBAAU,CAAC,MAAM,CAAS;gBAClC;gBAEA;;GAEC,GACD,aAAa,eAAe,MAAoB,EAA6C;oBAC3F,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CACnC,0BACA;oBAEF,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,cAAc,IAA0B,EAAiB;oBACpE,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CAAO,iCAAiC;oBAC9E,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,aAAa,QAAgB,EAAiB;oBACzD,MAAM,WAAW,MAAM,mBAAU,CAAC,MAAM,CAAO,CAAC,uBAAuB,EAAE,SAAS,CAAC;oBACnF,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,uBAAuB,IAAY,EAAoB;oBAClE,IAAI;wBACF,oBAAoB;wBACpB,mBAAmB;wBACnB,OAAO;oBACT,EAAE,OAAM;wBACN,OAAO;oBACT;gBACF;gBAEA;;GAEC,GACD,aAAa,eAIV;oBACD,oBAAoB;oBACpB,mBAAmB;oBACnB,MAAM,aAAa,MAAM,IAAI,CAAC,oBAAoB;oBAClD,MAAM,UAAU,MAAM,IAAI,CAAC,cAAc,CAAC;wBAAE,SAAS;wBAAG,UAAU;oBAAK;oBAEvE,MAAM,gBAAgB,QAAQ,IAAI,CAAC,MAAM,CAAC,CAAA,SAAU,OAAO,QAAQ,EAAE,MAAM;oBAC3E,MAAM,iBAAiB,QAAQ,IAAI,CAAC,MAAM,CAAC,CAAA;wBACzC,MAAM,aAAa,IAAI,KAAK,OAAO,cAAc;wBACjD,MAAM,UAAU,IAAI;wBACpB,QAAQ,OAAO,CAAC,QAAQ,OAAO,KAAK;wBACpC,OAAO,aAAa;oBACtB,GAAG,MAAM;oBAET,OAAO;wBACL,aAAa,WAAW,WAAW;wBACnC;wBACA;oBACF;gBACF;gBAEA;;GAEC,GACD,aAAa,cAAc,OAAe,EAAiC;oBACzE,MAAM,UAAU,MAAM,IAAI,CAAC,cAAc,CAAC;wBAAE,SAAS;wBAAG,UAAU;oBAAK;oBAEvE,qBAAqB;oBACrB,OAAO,QAAQ,IAAI,CAAC,MAAM,CAAC,CAAA,SACzB,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,WAAW,OACtD,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,WAAW;gBAE3D;gBAEA;;GAEC,GACD,aAAa,wBACX,SAAmB,EACnB,QAAiB,EACF;oBACf,iBAAiB;oBACjB,eAAe;oBACf,MAAM,WAAW,UAAU,GAAG,CAAC,OAAO;oBACpC,iBAAiB;oBACjB,qDAAqD;oBACvD;oBAEA,MAAM,QAAQ,GAAG,CAAC;gBACpB;YACF;gBAGA,WAAe;;;;;;;;;;;;;;;;;;;;;;;IDhLD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,uCAAsC;YAAC;YAAU;SAAsC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,8BAA6B;YAAC;SAAmB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;YAAS;SAAyB;QAAC,mCAAkC;YAAC;YAAS;SAAyB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,iDAAgD;YAAC;SAAgD;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,kCAAiC;YAAC;SAAwB;QAAC,wCAAuC;YAAC;SAAuC;IAAA;;AACh/B"}