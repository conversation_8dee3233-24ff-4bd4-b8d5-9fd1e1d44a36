package com.teammanage.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.teammanage.common.ApiResponse;
import com.teammanage.dto.request.UpdateUserProfileRequest;
import com.teammanage.dto.request.ValidatePasswordRequest;
import com.teammanage.dto.response.UserProfileResponse;
import com.teammanage.entity.Account;
import com.teammanage.service.UserService;
import com.teammanage.util.SecurityUtil;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;

/**
 * 用户控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/users")
@Tag(name = "用户管理", description = "用户信息管理相关接口")
@SecurityRequirement(name = "bearerAuth")
public class UserController {

    private static final Logger log = LoggerFactory.getLogger(UserController.class);

    @Autowired
    private UserService userService;

    /**
     * 获取当前用户资料
     */
    @GetMapping("/profile")
    @Operation(summary = "获取用户资料", description = "获取当前用户的个人资料")
    public ApiResponse<UserProfileResponse> getUserProfile() {
        Long userId = SecurityUtil.getCurrentUserId();
        UserProfileResponse response = userService.getUserProfile(userId);
        return ApiResponse.success(response);
    }

    /**
     * 更新用户资料
     */
    @PutMapping("/profile")
    @Operation(summary = "更新用户资料", description = "更新当前用户的个人资料")
    public ApiResponse<UserProfileResponse> updateUserProfile(@Valid @RequestBody UpdateUserProfileRequest request) {
        Long userId = SecurityUtil.getCurrentUserId();
        UserProfileResponse response = userService.updateUserProfile(userId, request);
        return ApiResponse.success("用户资料更新成功", response);
    }

    /**
     * 更新用户资料（兼容旧接口）
     * @deprecated 使用 PUT /profile 替代
     */
    @Deprecated
    @PostMapping("/profile/update")
    @Operation(summary = "更新用户资料", description = "更新当前用户的个人资料（已废弃，请使用PUT /profile）")
    public ApiResponse<UserProfileResponse> updateUserProfileLegacy(@Valid @RequestBody UpdateUserProfileRequest request) {
        Long userId = SecurityUtil.getCurrentUserId();
        UserProfileResponse response = userService.updateUserProfile(userId, request);
        return ApiResponse.success("用户资料更新成功", response);
    }

    /**
     * 根据邮箱搜索用户（用于添加好友）
     */
    @GetMapping("/search")
    @Operation(summary = "搜索用户", description = "根据邮箱关键词搜索用户，用于添加好友功能")
    public ApiResponse<List<Account>> searchUsers(
            @RequestParam
            @NotBlank(message = "邮箱关键词不能为空")
            String email) {
        Long currentUserId = SecurityUtil.getCurrentUserId();
        List<Account> users = userService.searchUsersByEmail(email, currentUserId);
        return ApiResponse.success(users);
    }

    /**
     * 验证密码
     */
    @PostMapping("/validate-password")
    @Operation(summary = "验证密码", description = "验证当前用户的密码是否正确")
    public ApiResponse<Boolean> validatePassword(@RequestBody @Valid ValidatePasswordRequest request) {
        Long userId = SecurityUtil.getCurrentUserId();
        boolean isValid = userService.validatePassword(userId, request.getPassword());
        return ApiResponse.success(isValid);
    }

    /**
     * 获取用户个人统计数据
     */
    @GetMapping("/personal-stats")
    @Operation(summary = "获取用户个人统计数据", description = "获取当前用户的车辆、人员、预警、告警统计数据")
    public ApiResponse<Map<String, Object>> getUserPersonalStats() {
        Long userId = SecurityUtil.getCurrentUserId();

        // 模拟统计数据，实际应该从数据库查询
        Map<String, Object> stats = new HashMap<>();
        stats.put("vehicles", 48);
        stats.put("personnel", 16);
        stats.put("warnings", 5);
        stats.put("alerts", 3);

        return ApiResponse.success(stats);
    }

    /**
     * 获取用户详细信息
     */
    @GetMapping("/profile-detail")
    @Operation(summary = "获取用户详细信息", description = "获取当前用户的详细个人信息")
    public ApiResponse<Map<String, Object>> getUserProfileDetail() {
        Long userId = SecurityUtil.getCurrentUserId();

        // 模拟用户详细信息，实际应该从数据库查询
        Map<String, Object> profileDetail = new HashMap<>();
        profileDetail.put("name", "张明");
        profileDetail.put("position", "车队管理员");
        profileDetail.put("email", "<EMAIL>");
        profileDetail.put("phone", "13800138000");
        profileDetail.put("registerDate", "2020年5月10日");
        profileDetail.put("lastLoginTime", "2025年7月25日 18:30:45");
        profileDetail.put("lastLoginTeam", "运输车队管理员");
        profileDetail.put("teamCount", 8);
        profileDetail.put("avatar", ""); // 头像URL，暂时为空

        return ApiResponse.success(profileDetail);
    }

}
