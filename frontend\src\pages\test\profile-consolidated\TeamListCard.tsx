import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  Card,
  Row,
  Col,
  Tag,
  Typography,
  Tooltip,
  List,
  Flex,
  Space,
  Spin,
  Alert
} from "antd";
import { TeamService } from "@/services/team";
import type { TeamDetailResponse } from "@/types/api";
import {
  CalendarOutlined,
  CarOutlined,
  TeamOutlined,
  UserOutlined,
  WarningOutlined,
  ClockCircleOutlined,
} from "@ant-design/icons";

const { Text } = Typography;

// 使用API类型定义，不需要重复定义接口

const TeamListCard: React.FC = () => {
  // 团队列表状态管理
  const [teams, setTeams] = useState<TeamDetailResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 获取团队列表数据
  useEffect(() => {
    console.log('TeamListCard: useEffect 开始执行');
    const fetchTeams = async () => {
      try {
        console.log('TeamListCard: 开始获取团队列表数据');
        setLoading(true);
        setError(null);
        const teamsData = await TeamService.getUserTeamsWithStats();
        console.log('TeamListCard: 获取到团队数据:', teamsData);
        setTeams(teamsData);
      } catch (error) {
        console.error('获取团队列表失败:', error);
        setError('获取团队列表失败');
      } finally {
        setLoading(false);
      }
    };

    fetchTeams();
  }, []);

  return (
    <Card
      className="dashboard-card"
      style={{
        borderRadius: 12,
        boxShadow: "0 4px 12px rgba(0,0,0,0.05)",
        border: "none",
        background: "linear-gradient(145deg, #ffffff, #f5f8ff)",
      }}
      title={
        <Flex justify="space-between" align="center">
          <Text strong>团队列表</Text>
        </Flex>
      }
    >
      {error ? (
        <Alert
          message="团队列表加载失败"
          description={error}
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
        />
      ) : (
        <Spin spinning={loading}>
          <List
            grid={{ gutter: 16, column: 2 }}
            dataSource={teams}
            renderItem={item => (
          <List.Item>
            <Card
              className="team-item"
              style={{
                background: "#fff",
                borderRadius: 8,
                borderLeft: `4px solid ${item.isCreator ? "#1890ff" : "#52c41a"}`,
                boxShadow: "0 1px 3px rgba(0,0,0,0.03)",
                height: "100%",
              }}
              bodyStyle={{
                padding: "16px",
              }}
            >
              <Card.Meta
                avatar={
                  <div
                    style={{
                      width: 48,
                      height: 48,
                      borderRadius: "50%",
                      background: item.isCreator ? "#1890ff" : "#52c41a",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      color: "white",
                      fontWeight: "bold",
                      fontSize: 20,
                    }}
                  >
                    {item.name.substring(0, 1)}
                  </div>
                }
                title={
                  <Flex align="center" wrap="wrap" gap={8}>
                    <Text strong>{item.name}</Text>
                  </Flex>
                }
                description={
                  <Space size={16} wrap>
                    <Tag
                      color={item.isCreator ? "blue" : "green"}
                      style={{ fontSize: 12 }}
                    >
                      {item.isCreator ? "管理员" : "成员"}
                    </Tag>
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      <TeamOutlined style={{ marginRight: 4, fontSize: 12 }} />
                      {item.memberCount}人
                    </Text>
                  </Space>
                }
              />

              <Card
                style={{
                  background: "#f9f9f9",
                  borderRadius: 6,
                  margin: "16px 0",
                }}
                bodyStyle={{ padding: "12px" }}
              >
                <Space direction="vertical" size={8} style={{ width: "100%" }}>
                  <Flex justify="space-between">
                    <Space size={8}>
                      <ClockCircleOutlined style={{ fontSize: 14, color: "#1890ff" }} />
                      <Text type="secondary" style={{ fontSize: 13 }}>创建时间</Text>
                    </Space>
                    <Text style={{ fontSize: 13 }}>
                      {new Date(item.createdAt).toLocaleString('zh-CN')}
                    </Text>
                  </Flex>
                  <Flex justify="space-between">
                    <Space size={8}>
                      <CalendarOutlined style={{ fontSize: 14, color: "#52c41a" }} />
                      <Text type="secondary" style={{ fontSize: 13 }}>最后更新</Text>
                    </Space>
                    <Text style={{ fontSize: 13 }}>
                      {new Date(item.updatedAt).toLocaleString('zh-CN')}
                    </Text>
                  </Flex>
                </Space>
              </Card>

              <Flex justify="space-between" gap={8} style={{ marginBottom: 16 }}>
                <Tooltip title="车辆资源">
                  <Card
                    style={{
                      background: "#f9f9f9",
                      borderRadius: 6,
                      flex: 1,
                    }}
                    bodyStyle={{ padding: "8px", textAlign: "center" }}
                  >
                    <CarOutlined style={{ color: "#1890ff", fontSize: 16 }} />
                    <Text strong style={{ color: "#1890ff", fontSize: 16, display: "block" }}>
                      {item.stats?.vehicles || 0}
                    </Text>
                  </Card>
                </Tooltip>

                <Tooltip title="人员资源">
                  <Card
                    style={{
                      background: "#f9f9f9",
                      borderRadius: 6,
                      flex: 1,
                    }}
                    bodyStyle={{ padding: "8px", textAlign: "center" }}
                  >
                    <UserOutlined style={{ color: "#52c41a", fontSize: 16 }} />
                    <Text strong style={{ color: "#52c41a", fontSize: 16, display: "block" }}>
                      {item.stats?.personnel || 0}
                    </Text>
                  </Card>
                </Tooltip>

                <Tooltip title="临期事项">
                  <Card
                    style={{
                      background: "#fff9e6",
                      borderRadius: 6,
                      flex: 1,
                    }}
                    bodyStyle={{ padding: "8px", textAlign: "center" }}
                  >
                    <WarningOutlined style={{ color: "#faad14", fontSize: 16 }} />
                    <Text strong style={{ color: "#faad14", fontSize: 16, display: "block" }}>
                      {item.stats?.expiring || 0}
                    </Text>
                  </Card>
                </Tooltip>

                <Tooltip title="逾期事项">
                  <Card
                    style={{
                      background: "#fff2f0",
                      borderRadius: 6,
                      flex: 1,
                    }}
                    bodyStyle={{ padding: "8px", textAlign: "center" }}
                  >
                    <WarningOutlined style={{ color: "#ff4d4f", fontSize: 16 }} />
                    <Text strong style={{ color: "#ff4d4f", fontSize: 16, display: "block" }}>
                      {item.stats?.overdue || 0}
                    </Text>
                  </Card>
                </Tooltip>
              </Flex>

              <Space.Compact block>
                <Button block style={{ fontWeight: 500 }}>
                  进入
                </Button>
              </Space.Compact>
            </Card>
          </List.Item>
        )}
          />
        </Spin>
      )}
    </Card>
  );
};

export default TeamListCard;