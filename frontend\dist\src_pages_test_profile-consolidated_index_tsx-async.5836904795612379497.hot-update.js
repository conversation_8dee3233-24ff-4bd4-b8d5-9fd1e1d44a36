globalThis.makoModuleHotUpdate('src/pages/test/profile-consolidated/index.tsx', {
    modules: {
        "src/pages/test/profile-consolidated/TodoManagement.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _todo = __mako_require__("src/services/todo.ts");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Text } = _antd.Typography;
            const { TabPane } = _antd.Tabs;
            const TodoManagement = (props)=>{
                _s();
                // TODO数据状态管理
                const [personalTasks, setPersonalTasks] = (0, _react.useState)([]);
                const [todoStats, setTodoStats] = (0, _react.useState)({
                    highPriorityCount: 0,
                    mediumPriorityCount: 0,
                    lowPriorityCount: 0,
                    totalCount: 0,
                    completedCount: 0,
                    completionPercentage: 0
                });
                const [loading, setLoading] = (0, _react.useState)(true);
                const [error, setError] = (0, _react.useState)(null);
                // 待办事项状态管理
                const [todoModalVisible, setTodoModalVisible] = (0, _react.useState)(false);
                const [todoForm] = _antd.Form.useForm();
                const [editingTodoId, setEditingTodoId] = (0, _react.useState)(null);
                // 过滤器状态
                const [activeTab, setActiveTab] = (0, _react.useState)("pending");
                const [searchText, setSearchText] = (0, _react.useState)("");
                // 获取TODO数据
                (0, _react.useEffect)(()=>{
                    const fetchTodoData = async ()=>{
                        try {
                            setLoading(true);
                            setError(null);
                            const [todos, stats] = await Promise.all([
                                _todo.TodoService.getUserTodos(),
                                _todo.TodoService.getTodoStats()
                            ]);
                            setPersonalTasks(todos);
                            setTodoStats(stats);
                        } catch (error) {
                            console.error('获取TODO数据失败:', error);
                            setError('获取TODO数据失败');
                        } finally{
                            setLoading(false);
                        }
                    };
                    fetchTodoData();
                }, []);
                // 根据激活的标签和搜索文本过滤任务
                const filteredPersonalTasks = personalTasks.filter((task)=>{
                    // 根据标签过滤
                    if (activeTab === "pending" && task.status === 1) return false;
                    if (activeTab === "completed" && task.status === 0) return false;
                    // 根据搜索文本过滤
                    if (searchText && !task.title.toLowerCase().includes(searchText.toLowerCase())) return false;
                    return true;
                });
                // 处理待办事项操作
                const handleToggleTodoStatus = async (id)=>{
                    try {
                        const task = personalTasks.find((t)=>t.id === id);
                        if (!task) return;
                        const newStatus = task.status === 0 ? 1 : 0;
                        await _todo.TodoService.updateTodo(id, {
                            status: newStatus
                        });
                        // 更新本地状态
                        setPersonalTasks(personalTasks.map((task)=>task.id === id ? {
                                ...task,
                                status: newStatus
                            } : task));
                        // 刷新统计数据
                        const stats = await _todo.TodoService.getTodoStats();
                        setTodoStats(stats);
                        _antd.message.success(newStatus === 1 ? '任务已完成' : '任务已标记为未完成');
                    } catch (error) {
                        console.error('更新任务状态失败:', error);
                        _antd.message.error('更新任务状态失败');
                    }
                };
                const handleAddOrUpdateTodo = async (values)=>{
                    try {
                        if (editingTodoId) {
                            // 更新现有待办事项
                            const updatedTodo = await _todo.TodoService.updateTodo(editingTodoId, {
                                title: values.name,
                                priority: values.priority
                            });
                            setPersonalTasks(personalTasks.map((task)=>task.id === editingTodoId ? updatedTodo : task));
                            _antd.message.success('任务更新成功');
                        } else {
                            // 添加新待办事项
                            const newTodo = await _todo.TodoService.createTodo({
                                title: values.name,
                                priority: values.priority
                            });
                            setPersonalTasks([
                                newTodo,
                                ...personalTasks
                            ]);
                            _antd.message.success('任务创建成功');
                        }
                        // 刷新统计数据
                        const stats = await _todo.TodoService.getTodoStats();
                        setTodoStats(stats);
                        // 重置表单并关闭模态框
                        setTodoModalVisible(false);
                        setEditingTodoId(null);
                        todoForm.resetFields();
                    } catch (error) {
                        console.error('保存任务失败:', error);
                        _antd.message.error('保存任务失败');
                    }
                };
                const handleDeleteTodo = async (id)=>{
                    try {
                        await _todo.TodoService.deleteTodo(id);
                        setPersonalTasks(personalTasks.filter((task)=>task.id !== id));
                        // 刷新统计数据
                        const stats = await _todo.TodoService.getTodoStats();
                        setTodoStats(stats);
                        _antd.message.success('任务删除成功');
                    } catch (error) {
                        console.error('删除任务失败:', error);
                        _antd.message.error('删除任务失败');
                    }
                };
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                    className: "dashboard-card",
                    style: {
                        borderRadius: 12,
                        boxShadow: "0 4px 12px rgba(0,0,0,0.05)",
                        border: "none",
                        background: "linear-gradient(145deg, #ffffff, #f5f8ff)"
                    },
                    title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                        justify: "space-between",
                        align: "center",
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                            strong: true,
                            children: "待办事项"
                        }, void 0, false, {
                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                            lineNumber: 206,
                            columnNumber: 11
                        }, void 0)
                    }, void 0, false, {
                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                        lineNumber: 205,
                        columnNumber: 9
                    }, void 0),
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                            justify: "space-between",
                            align: "center",
                            style: {
                                marginBottom: 16,
                                gap: 16,
                                flexWrap: "wrap"
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                    size: 16,
                                    style: {
                                        flex: 1,
                                        minWidth: 300
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input.Search, {
                                            placeholder: "搜索任务...",
                                            allowClear: true,
                                            prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SearchOutlined, {}, void 0, false, {
                                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                lineNumber: 221,
                                                columnNumber: 21
                                            }, void 0),
                                            value: searchText,
                                            onChange: (e)=>setSearchText(e.target.value),
                                            style: {
                                                flex: 1
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                            lineNumber: 218,
                                            columnNumber: 11
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            type: "primary",
                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PlusOutlined, {}, void 0, false, {
                                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                lineNumber: 230,
                                                columnNumber: 19
                                            }, void 0),
                                            onClick: ()=>{
                                                setEditingTodoId(null);
                                                todoForm.resetFields();
                                                setTodoModalVisible(true);
                                            },
                                            style: {
                                                background: "#1890ff",
                                                borderColor: "#1890ff",
                                                boxShadow: "0 2px 4px rgba(24, 144, 255, 0.3)",
                                                fontWeight: 500
                                            },
                                            children: "新增"
                                        }, void 0, false, {
                                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                            lineNumber: 228,
                                            columnNumber: 11
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                    lineNumber: 216,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {
                                    type: "vertical",
                                    style: {
                                        height: 20,
                                        backgroundColor: "#e8e8e8"
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                    lineNumber: 247,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                    align: "center",
                                    size: 16,
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            size: 8,
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                                    title: "高优先级任务",
                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                        align: "center",
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                style: {
                                                                    width: 10,
                                                                    height: 10,
                                                                    borderRadius: "50%",
                                                                    background: "#ff4d4f",
                                                                    marginRight: 4
                                                                }
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                                lineNumber: 254,
                                                                columnNumber: 17
                                                            }, this),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                style: {
                                                                    fontSize: 13,
                                                                    fontWeight: 500
                                                                },
                                                                children: todoStats.highPriorityCount
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                                lineNumber: 263,
                                                                columnNumber: 17
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                        lineNumber: 253,
                                                        columnNumber: 15
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                    lineNumber: 252,
                                                    columnNumber: 13
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                                    title: "中优先级任务",
                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                        align: "center",
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                style: {
                                                                    width: 10,
                                                                    height: 10,
                                                                    borderRadius: "50%",
                                                                    background: "#faad14",
                                                                    marginRight: 4
                                                                }
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                                lineNumber: 271,
                                                                columnNumber: 17
                                                            }, this),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                style: {
                                                                    fontSize: 13,
                                                                    fontWeight: 500
                                                                },
                                                                children: todoStats.mediumPriorityCount
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                                lineNumber: 280,
                                                                columnNumber: 17
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                        lineNumber: 270,
                                                        columnNumber: 15
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                    lineNumber: 269,
                                                    columnNumber: 13
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                                    title: "低优先级任务",
                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                        align: "center",
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                style: {
                                                                    width: 10,
                                                                    height: 10,
                                                                    borderRadius: "50%",
                                                                    background: "#52c41a",
                                                                    marginRight: 4
                                                                }
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                                lineNumber: 288,
                                                                columnNumber: 17
                                                            }, this),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                style: {
                                                                    fontSize: 13,
                                                                    fontWeight: 500
                                                                },
                                                                children: todoStats.lowPriorityCount
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                                lineNumber: 297,
                                                                columnNumber: 17
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                        lineNumber: 287,
                                                        columnNumber: 15
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                    lineNumber: 286,
                                                    columnNumber: 13
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                            lineNumber: 251,
                                            columnNumber: 11
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {
                                            type: "vertical",
                                            style: {
                                                height: 20,
                                                backgroundColor: "#e8e8e8"
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                            lineNumber: 304,
                                            columnNumber: 11
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                            title: "任务完成进度",
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Progress, {
                                                percent: todoStats.completionPercentage,
                                                size: "small",
                                                style: {
                                                    width: 120
                                                },
                                                strokeColor: "#52c41a"
                                            }, void 0, false, {
                                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                lineNumber: 308,
                                                columnNumber: 13
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                            lineNumber: 307,
                                            columnNumber: 11
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                    lineNumber: 249,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                            lineNumber: 211,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tabs, {
                            activeKey: activeTab,
                            onChange: (key)=>setActiveTab(key),
                            size: "middle",
                            style: {
                                marginBottom: 8
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TabPane, {
                                    tab: "全部"
                                }, "all", false, {
                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                    lineNumber: 327,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TabPane, {
                                    tab: "待处理"
                                }, "pending", false, {
                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                    lineNumber: 328,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TabPane, {
                                    tab: "已完成"
                                }, "completed", false, {
                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                    lineNumber: 329,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                            lineNumber: 319,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List, {
                            dataSource: filteredPersonalTasks,
                            renderItem: (item)=>{
                                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item, {
                                    className: "todo-item",
                                    style: {
                                        padding: "10px 16px",
                                        marginBottom: 12,
                                        borderRadius: 8,
                                        background: "#fff",
                                        opacity: item.completed ? 0.7 : 1,
                                        borderLeft: `3px solid ${item.completed ? "#52c41a" : item.priority === "high" ? "#ff4d4f" : item.priority === "medium" ? "#faad14" : "#8c8c8c"}`,
                                        boxShadow: "0 1px 4px rgba(0,0,0,0.05)"
                                    },
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                        align: "center",
                                        gap: 12,
                                        style: {
                                            width: "100%"
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                vertical: true,
                                                align: "center",
                                                children: [
                                                    item.completed ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                        align: "center",
                                                        justify: "center",
                                                        style: {
                                                            width: 22,
                                                            height: 22,
                                                            borderRadius: "50%",
                                                            background: "#52c41a"
                                                        },
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckOutlined, {
                                                            style: {
                                                                color: "#fff",
                                                                fontSize: 12
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                            lineNumber: 371,
                                                            columnNumber: 23
                                                        }, void 0)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                        lineNumber: 361,
                                                        columnNumber: 21
                                                    }, void 0) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        style: {
                                                            width: 18,
                                                            height: 18,
                                                            borderRadius: "50%",
                                                            border: `2px solid ${item.priority === "high" ? "#ff4d4f" : item.priority === "medium" ? "#faad14" : "#8c8c8c"}`
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                        lineNumber: 376,
                                                        columnNumber: 21
                                                    }, void 0),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        style: {
                                                            width: 2,
                                                            height: 24,
                                                            background: "#f0f0f0",
                                                            marginTop: 4
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                        lineNumber: 392,
                                                        columnNumber: 19
                                                    }, void 0)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                lineNumber: 359,
                                                columnNumber: 17
                                            }, void 0),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                vertical: true,
                                                style: {
                                                    flex: 1
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        style: {
                                                            fontSize: 14,
                                                            fontWeight: item.priority === "high" ? 500 : "normal",
                                                            textDecoration: item.completed ? "line-through" : "none",
                                                            color: item.completed ? "#8c8c8c" : "#262626"
                                                        },
                                                        children: item.name
                                                    }, void 0, false, {
                                                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                        lineNumber: 404,
                                                        columnNumber: 19
                                                    }, void 0),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                        align: "center",
                                                        size: 6,
                                                        style: {
                                                            marginTop: 4
                                                        },
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CalendarOutlined, {
                                                                style: {
                                                                    fontSize: 12,
                                                                    color: "#8c8c8c"
                                                                }
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                                lineNumber: 420,
                                                                columnNumber: 21
                                                            }, void 0),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                type: "secondary",
                                                                style: {
                                                                    fontSize: 12
                                                                },
                                                                children: [
                                                                    "创建于: ",
                                                                    item.createDate
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                                lineNumber: 426,
                                                                columnNumber: 21
                                                            }, void 0)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                        lineNumber: 419,
                                                        columnNumber: 19
                                                    }, void 0)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                lineNumber: 403,
                                                columnNumber: 17
                                            }, void 0),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Dropdown, {
                                                trigger: [
                                                    'click'
                                                ],
                                                menu: {
                                                    items: [
                                                        {
                                                            key: 'complete',
                                                            label: item.completed ? '标记未完成' : '标记完成',
                                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckOutlined, {
                                                                style: {
                                                                    color: item.completed ? '#8c8c8c' : '#52c41a',
                                                                    fontSize: 14
                                                                }
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                                lineNumber: 441,
                                                                columnNumber: 27
                                                            }, void 0)
                                                        },
                                                        {
                                                            key: 'edit',
                                                            label: '编辑任务',
                                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {
                                                                style: {
                                                                    color: '#8c8c8c'
                                                                }
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                                lineNumber: 452,
                                                                columnNumber: 31
                                                            }, void 0)
                                                        },
                                                        {
                                                            key: 'delete',
                                                            label: '删除任务',
                                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {
                                                                style: {
                                                                    color: '#ff4d4f'
                                                                }
                                                            }, void 0, false, {
                                                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                                lineNumber: 457,
                                                                columnNumber: 31
                                                            }, void 0),
                                                            danger: true
                                                        }
                                                    ],
                                                    onClick: ({ key })=>{
                                                        if (key === "complete") handleToggleTodoStatus(item.id);
                                                        else if (key === "edit") {
                                                            setEditingTodoId(item.id);
                                                            todoForm.setFieldsValue({
                                                                name: item.name,
                                                                priority: item.priority
                                                            });
                                                            setTodoModalVisible(true);
                                                        } else if (key === "delete") handleDeleteTodo(item.id);
                                                    }
                                                },
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    type: "text",
                                                    size: "small",
                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MoreOutlined, {}, void 0, false, {
                                                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                        lineNumber: 480,
                                                        columnNumber: 27
                                                    }, void 0),
                                                    style: {
                                                        width: 32,
                                                        height: 32
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                    lineNumber: 477,
                                                    columnNumber: 19
                                                }, void 0)
                                            }, void 0, false, {
                                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                                lineNumber: 433,
                                                columnNumber: 17
                                            }, void 0)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                        lineNumber: 357,
                                        columnNumber: 15
                                    }, void 0)
                                }, void 0, false, {
                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                    lineNumber: 337,
                                    columnNumber: 13
                                }, void 0);
                            }
                        }, void 0, false, {
                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                            lineNumber: 333,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                            title: editingTodoId ? "编辑待办事项" : "新增待办事项",
                            open: todoModalVisible,
                            onCancel: ()=>{
                                setTodoModalVisible(false);
                                todoForm.resetFields();
                            },
                            onOk: ()=>{
                                todoForm.submit();
                            },
                            centered: true,
                            destroyOnClose: true,
                            footer: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    onClick: ()=>setTodoModalVisible(false),
                                    children: "取消"
                                }, "cancel", false, {
                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                    lineNumber: 504,
                                    columnNumber: 11
                                }, void 0),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    type: "primary",
                                    onClick: ()=>{
                                        todoForm.submit();
                                    },
                                    style: {
                                        background: "#1890ff",
                                        borderColor: "#1890ff",
                                        boxShadow: "0 2px 4px rgba(24, 144, 255, 0.3)"
                                    },
                                    children: editingTodoId ? "更新任务" : "创建任务"
                                }, "submit", false, {
                                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                    lineNumber: 507,
                                    columnNumber: 11
                                }, void 0)
                            ],
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                                form: todoForm,
                                layout: "vertical",
                                onFinish: handleAddOrUpdateTodo,
                                autoComplete: "off",
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        name: "name",
                                        label: "任务名称",
                                        rules: [
                                            {
                                                required: true,
                                                message: "请输入任务名称"
                                            }
                                        ],
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                            placeholder: "请输入任务名称",
                                            size: "large",
                                            style: {
                                                borderRadius: 6
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                            lineNumber: 534,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                        lineNumber: 529,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        name: "priority",
                                        label: "优先级",
                                        initialValue: "medium",
                                        rules: [
                                            {
                                                required: true,
                                                message: "请选择优先级"
                                            }
                                        ],
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select, {
                                            size: "large",
                                            options: [
                                                {
                                                    value: "high",
                                                    label: "高优先级"
                                                },
                                                {
                                                    value: "medium",
                                                    label: "中优先级"
                                                },
                                                {
                                                    value: "low",
                                                    label: "低优先级"
                                                }
                                            ],
                                            style: {
                                                borderRadius: 6
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                            lineNumber: 547,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                        lineNumber: 541,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                                lineNumber: 523,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                            lineNumber: 491,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/test/profile-consolidated/TodoManagement.tsx",
                    lineNumber: 196,
                    columnNumber: 5
                }, this);
            };
            _s(TodoManagement, "HHPhC8rlUAXUyFFDx+yADUZ6q1M=", false, function() {
                return [
                    _antd.Form.useForm
                ];
            });
            _c = TodoManagement;
            var _default = TodoManagement;
            var _c;
            $RefreshReg$(_c, "TodoManagement");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '11365412631849899442';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/.umi/plugin-openapi/openapi.tsx": [
            "vendors",
            "src/.umi/plugin-openapi/openapi.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/friend/index.tsx": [
            "p__friend__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "common",
            "p__subscription__index"
        ],
        "src/pages/team/detail/index.tsx": [
            "common",
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "p__team__index"
        ],
        "src/pages/test/profile-consolidated/index.tsx": [
            "src/pages/test/profile-consolidated/index.tsx"
        ],
        "src/pages/user/index.tsx": [
            "common",
            "p__user__index"
        ],
        "src/pages/user/login/index.tsx": [
            "p__user__login__index"
        ],
        "src/pages/user/team-select/index.tsx": [
            "src/pages/user/team-select/index.tsx"
        ]
    });
    ;
});

//# sourceMappingURL=src_pages_test_profile-consolidated_index_tsx-async.5836904795612379497.hot-update.js.map