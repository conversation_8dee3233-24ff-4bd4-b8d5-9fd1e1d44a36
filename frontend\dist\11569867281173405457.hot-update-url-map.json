{"js": {"src/pages/test/profile-consolidated/index.tsx": "src_pages_test_profile-consolidated_index_tsx-async.js", "p__help__index": "p__help__index-async.js", "p__team__index": "p__team__index-async.js", "p__friend__index": "p__friend__index-async.js", "p__team__detail__index": "p__team__detail__index-async.js", "p__user__login__index": "p__user__login__index-async.js", "src/.umi/umi.ts?hmr": "umi.js", "vendors": "vendors-async.js", "src/.umi/plugin-layout/Layout.tsx": "src__umi_plugin-layout_Layout_tsx-async.js", "p__404": "p__404-async.js", "src/pages/personal-center/index.tsx": "src_pages_personal-center_index_tsx-async.js", "common": "common-async.js", "src/.umi/core/EmptyRoute.tsx": "src__umi_core_EmptyRoute_tsx-async.js", "src/.umi/plugin-openapi/openapi.tsx": "src__umi_plugin-openapi_openapi_tsx-async.js", "p__user__index": "p__user__index-async.js", "p__Dashboard__index": "p__Dashboard__index-async.js", "p__subscription__index": "p__subscription__index-async.js", "src/pages/user/team-select/index.tsx": "src_pages_user_team-select_index_tsx-async.js"}, "css": {"src/.umi/umi.ts?hmr": "umi.css", "src/.umi/plugin-layout/Layout.tsx": "src__umi_plugin-layout_Layout_tsx-async.css", "vendors": "vendors-async.css"}}