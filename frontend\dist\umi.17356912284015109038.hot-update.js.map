{"version": 3, "sources": ["umi.17356912284015109038.hot-update.js", "src/types/api.ts"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/.umi/umi.ts?hmr',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='6639027343615962924';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/.umi/plugin-openapi/openapi.tsx\":[\"vendors\",\"src/.umi/plugin-openapi/openapi.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/friend/index.tsx\":[\"p__friend__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"common\",\"p__subscription__index\"],\"src/pages/team/detail/index.tsx\":[\"common\",\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"p__team__index\"],\"src/pages/test/profile-consolidated/index.tsx\":[\"src/pages/test/profile-consolidated/index.tsx\"],\"src/pages/user/index.tsx\":[\"common\",\"p__user__index\"],\"src/pages/user/login/index.tsx\":[\"p__user__login__index\"],\"src/pages/user/team-select/index.tsx\":[\"src/pages/user/team-select/index.tsx\"]});;\r\n  },\r\n);\r\n", "/**\n * API 相关类型定义\n * 基于后端 DTO 和 Entity 类生成\n */\n\n// ============= 基础类型 =============\n\n/**\n * API 响应基础结构\n */\nexport interface ApiResponse<T = any> {\n  code: number;\n  message: string;\n  data: T;\n  timestamp: string;\n}\n\n/**\n * 分页请求参数\n */\nexport interface PageRequest {\n  current?: number;\n  pageSize?: number;\n}\n\n/**\n * 分页响应结构\n */\nexport interface PageResponse<T> {\n  list: T[];\n  total: number;\n  current: number;\n  pageSize: number;\n}\n\n// ============= 认证相关类型 =============\n\n/**\n * 登录请求\n */\nexport interface LoginRequest {\n  email: string;\n  password: string;\n}\n\n/**\n * 注册请求\n */\nexport interface RegisterRequest {\n  email: string;\n  password: string;\n  name: string;\n}\n\n/**\n * 用户信息\n */\nexport interface UserInfo {\n  id: number;\n  email: string;\n  name: string;\n}\n\n/**\n * 团队信息（登录响应中的简化版本）\n */\nexport interface TeamInfo {\n  id: number;\n  name: string;\n  isCreator: boolean;\n  memberCount: number;\n  lastAccessTime: string;\n}\n\n/**\n * 登录响应\n */\nexport interface LoginResponse {\n  token: string;\n  expiresIn: number;\n  user: UserInfo;\n  teams: TeamInfo[];\n  /** 当前选择的团队信息（用于团队选择响应） */\n  team?: TeamInfo;\n  /** 团队选择成功标识（用于团队选择响应） */\n  teamSelectionSuccess?: boolean;\n}\n\n// ============= 团队管理相关类型 =============\n\n/**\n * 创建团队请求\n */\nexport interface CreateTeamRequest {\n  name: string;\n  description?: string;\n}\n\n/**\n * 更新团队请求\n */\nexport interface UpdateTeamRequest {\n  name: string;\n  description?: string;\n}\n\n/**\n * 邀请成员请求\n */\nexport interface InviteMembersRequest {\n  emails: string[];\n}\n\n/**\n * 团队详情响应\n */\nexport interface TeamDetailResponse {\n  id: number;\n  name: string;\n  description?: string;\n  createdBy: number;\n  memberCount: number;\n  isCreator: boolean;\n  createdAt: string;\n  updatedAt: string;\n  stats?: TeamStatsData; // 可选的统计数据\n}\n\n/**\n * 团队成员响应\n */\nexport interface TeamMemberResponse {\n  id: number;\n  accountId: number;\n  email: string;\n  name: string;\n  isCreator: boolean;\n  assignedAt: string;\n  lastAccessTime: string;\n  isActive: boolean;\n}\n\n// ============= 用户管理相关类型 =============\n\n/**\n * 用户资料响应\n */\nexport interface UserProfileResponse {\n  id: number;\n  email: string;\n  name: string;\n  defaultSubscriptionPlanId: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\n/**\n * 更新用户资料请求\n */\nexport interface UpdateUserProfileRequest {\n  name?: string;\n  currentPassword?: string;\n  newPassword?: string;\n}\n\n// ============= 订阅管理相关类型 =============\n\n/**\n * 订阅套餐响应\n */\nexport interface SubscriptionPlanResponse {\n  id: number;\n  name: string;\n  description: string;\n  maxSize: number;\n  price: number;\n  isActive: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\n/**\n * 创建订阅请求\n */\nexport interface CreateSubscriptionRequest {\n  planId: number;\n  duration: number;\n}\n\n/**\n * 订阅状态枚举\n */\nexport enum SubscriptionStatus {\n  ACTIVE = 'ACTIVE',\n  EXPIRED = 'EXPIRED',\n  CANCELLED = 'CANCELLED'\n}\n\n/**\n * 订阅响应\n */\nexport interface SubscriptionResponse {\n  id: number;\n  accountId: number;\n  subscriptionPlanId: number;\n  planName: string;\n  planDescription: string;\n  maxSize: number;\n  price: number;\n  startDate: string;\n  endDate: string;\n  status: SubscriptionStatus;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// ============= 实体类型 =============\n\n/**\n * 账户实体\n */\nexport interface Account {\n  id: number;\n  email: string;\n  name: string;\n  defaultSubscriptionPlanId: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\n/**\n * 包含备注信息的好友响应\n */\nexport interface FriendWithRemark {\n  id: number;\n  email: string;\n  name: string;\n  remark?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n/**\n * 团队实体\n */\nexport interface Team {\n  id: number;\n  name: string;\n  description?: string;\n  createdBy: number;\n  isDeleted: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\n/**\n * 团队成员实体\n */\nexport interface TeamMember {\n  id: number;\n  teamId: number;\n  accountId: number;\n  isCreator: boolean;\n  assignedAt: string;\n  lastAccessTime: string;\n  isActive: boolean;\n  isDeleted: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\n/**\n * 订阅套餐实体\n */\nexport interface SubscriptionPlan {\n  id: number;\n  name: string;\n  description: string;\n  maxSize: number;\n  price: number;\n  isActive: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\n/**\n * 账户订阅实体\n */\nexport interface AccountSubscription {\n  id: number;\n  accountId: number;\n  subscriptionPlanId: number;\n  startDate: string;\n  endDate: string;\n  status: SubscriptionStatus;\n  createdAt: string;\n  updatedAt: string;\n}\n\n/**\n * 好友关系状态枚举\n */\nexport enum FriendStatus {\n  PENDING = 'pending',\n  ACCEPTED = 'accepted',\n  REJECTED = 'rejected'\n}\n\n/**\n * 账户关系实体\n */\nexport interface AccountRelation {\n  id: number;\n  accountId: number;\n  invitedBy: number;\n  invitedAt: string;\n  remark?: string;\n  status: FriendStatus;\n  requestedAt: string;\n  isActive: boolean;\n  isDeleted: boolean;\n  updatedAt: string;\n}\n\n// ============= 好友管理相关类型 =============\n\n/**\n * 添加好友请求\n */\nexport interface AddFriendRequest {\n  email: string;\n}\n\n/**\n * 设置好友备注请求\n */\nexport interface SetFriendRemarkRequest {\n  friendId: number;\n  remark: string;\n}\n\n/**\n * 从好友列表邀请成员请求\n */\nexport interface InviteFriendsRequest {\n  friendIds: number[];\n}\n\n// ============= 用户统计相关类型 =============\n\n/**\n * 用户个人统计数据响应\n */\nexport interface UserPersonalStatsResponse {\n  vehicles: number;\n  personnel: number;\n  warnings: number;\n  alerts: number;\n}\n\n/**\n * 团队统计数据\n */\nexport interface TeamStatsData {\n  vehicles: number;\n  personnel: number;\n  expiring: number;\n  overdue: number;\n}\n\n/**\n * 用户详细信息响应\n */\nexport interface UserProfileDetailResponse {\n  name: string;\n  position: string;\n  email: string;\n  phone: string;\n  registerDate: string;\n  lastLoginTime: string;\n  lastLoginTeam: string;\n  teamCount: number;\n  avatar?: string;\n}\n\n// ============= TODO相关类型 =============\n\n/**\n * TODO响应\n */\nexport interface TodoResponse {\n  id: number;\n  title: string;\n  description?: string;\n  status: number; // 0-未完成，1-已完成\n  priority: number; // 1-低，2-中，3-高\n  userId: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\n/**\n * 创建TODO请求\n */\nexport interface CreateTodoRequest {\n  title: string;\n  description?: string;\n  priority: number;\n}\n\n/**\n * 更新TODO请求\n */\nexport interface UpdateTodoRequest {\n  title?: string;\n  description?: string;\n  status?: number;\n  priority?: number;\n}\n\n/**\n * TODO统计信息响应\n */\nexport interface TodoStatsResponse {\n  highPriorityCount: number;\n  mediumPriorityCount: number;\n  lowPriorityCount: number;\n  totalCount: number;\n  completedCount: number;\n  completionPercentage: number;\n}\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uBACA;IACE,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sBC6LD;;;;eAAA,uBAAA;;sBA8GA;;;;eAAA,iBAAA;;;;;;;;;;;;;;;;;;;;;;;ID3SE;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,uCAAsC;YAAC;YAAU;SAAsC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,8BAA6B;YAAC;SAAmB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;YAAS;SAAyB;QAAC,mCAAkC;YAAC;YAAS;SAAyB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,iDAAgD;YAAC;SAAgD;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,kCAAiC;YAAC;SAAwB;QAAC,wCAAuC;YAAC;SAAuC;IAAA;;AACh/B"}