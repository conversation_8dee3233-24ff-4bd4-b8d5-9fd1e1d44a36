{"version": 3, "sources": ["src_pages_test_profile-consolidated_index_tsx-async.13182230788122355759.hot-update.js", "src/pages/test/profile-consolidated/UserProfileCard.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/test/profile-consolidated/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='8373405337038599514';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/.umi/plugin-openapi/openapi.tsx\":[\"vendors\",\"src/.umi/plugin-openapi/openapi.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/friend/index.tsx\":[\"p__friend__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"common\",\"p__subscription__index\"],\"src/pages/team/detail/index.tsx\":[\"common\",\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"p__team__index\"],\"src/pages/test/profile-consolidated/index.tsx\":[\"src/pages/test/profile-consolidated/index.tsx\"],\"src/pages/user/index.tsx\":[\"common\",\"p__user__index\"],\"src/pages/user/login/index.tsx\":[\"p__user__login__index\"],\"src/pages/user/team-select/index.tsx\":[\"src/pages/user/team-select/index.tsx\"]});;\r\n  },\r\n);\r\n", "import {\r\n  CalendarOutlined,\r\n  CarOutlined,\r\n  CheckOutlined,\r\n  EditOutlined,\r\n  LogoutOutlined,\r\n  MailOutlined,\r\n  PhoneOutlined,\r\n  SettingOutlined,\r\n  TagOutlined,\r\n  TeamOutlined,\r\n  UserOutlined,\r\n  WarningOutlined,\r\n} from \"@ant-design/icons\";\r\nimport {\r\n  Avatar,\r\n  Button,\r\n  Card,\r\n  Col,\r\n  Divider,\r\n  Dropdown,\r\n  Flex,\r\n  Form,\r\n  Input,\r\n  Menu,\r\n  Modal,\r\n  Row,\r\n  Space,\r\n  Steps,\r\n  Tag,\r\n  Tooltip,\r\n  Typography,\r\n  Spin,\r\n  Alert,\r\n} from \"antd\";\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { UserService } from \"@/services/user\";\r\nimport type { UserPersonalStatsResponse, UserProfileDetailResponse } from \"@/types/api\";\r\n\r\nconst { Title, Text } = Typography;\r\nconst { Step } = Steps;\r\n\r\nconst UserProfileCard: React.FC = () => {\r\n  // 个人信息数据\r\n  const userInfo = {\r\n    name: \"张明\",\r\n    position: \"车队管理员\",\r\n    email: \"<EMAIL>\",\r\n    phone: \"13800138000\",\r\n    registerDate: \"2020年5月10日\",\r\n    lastLoginTime: \"2025年7月25日 18:30:45\",\r\n    lastLoginTeam: \"运输车队管理员\",\r\n    teamCount: 8, // 新增团队总数\r\n  };\r\n\r\n  // 个人统计数据状态\r\n  const [personalStats, setPersonalStats] = useState<UserPersonalStatsResponse>({\r\n    vehicles: 0,\r\n    personnel: 0,\r\n    warnings: 0,\r\n    alerts: 0,\r\n  });\r\n  const [statsLoading, setStatsLoading] = useState(true);\r\n  const [statsError, setStatsError] = useState<string | null>(null);\r\n\r\n  // 订阅计划数据\r\n  const subscriptionPlans = [\r\n    {\r\n      id: \"basic\",\r\n      name: \"基础版\",\r\n      price: 0,\r\n      description: \"适合小团队使用\",\r\n      features: [\"最多5个团队\", \"最多20辆车辆\", \"基础安全监控\", \"基本报告功能\"],\r\n    },\r\n    {\r\n      id: \"professional\",\r\n      name: \"专业版\",\r\n      price: 199,\r\n      description: \"适合中小型企业\",\r\n      features: [\r\n        \"最多20个团队\",\r\n        \"最多100辆车辆\",\r\n        \"高级安全监控\",\r\n        \"详细分析报告\",\r\n        \"设备状态预警\",\r\n        \"优先技术支持\",\r\n      ],\r\n    },\r\n    {\r\n      id: \"enterprise\",\r\n      name: \"企业版\",\r\n      price: 499,\r\n      description: \"适合大型企业\",\r\n      features: [\r\n        \"不限团队数量\",\r\n        \"不限车辆数量\",\r\n        \"AI安全分析\",\r\n        \"实时监控告警\",\r\n        \"定制化报告\",\r\n        \"专属客户经理\",\r\n        \"24/7技术支持\",\r\n      ],\r\n    },\r\n  ];\r\n\r\n  // 当前订阅信息\r\n  const currentSubscription = {\r\n    planId: \"basic\",\r\n    expires: \"2025-12-31\",\r\n  };\r\n\r\n  // 状态管理\r\n  const [editProfileModalVisible, setEditProfileModalVisible] = useState(false);\r\n  const [subscriptionModalVisible, setSubscriptionModalVisible] =\r\n    useState(false);\r\n  const [currentStep, setCurrentStep] = useState(0);\r\n  const [editProfileForm] = Form.useForm();\r\n\r\n  // 获取用户统计数据\r\n  useEffect(() => {\r\n    console.log('UserProfileCard: useEffect 开始执行');\r\n    const fetchPersonalStats = async () => {\r\n      try {\r\n        console.log('UserProfileCard: 开始获取用户统计数据');\r\n        setStatsLoading(true);\r\n        setStatsError(null);\r\n        const stats = await UserService.getUserPersonalStats();\r\n        console.log('UserProfileCard: 获取到统计数据:', stats);\r\n        setPersonalStats(stats);\r\n      } catch (error) {\r\n        console.error('获取用户统计数据失败:', error);\r\n        setStatsError('获取统计数据失败');\r\n      } finally {\r\n        setStatsLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchPersonalStats();\r\n  }, []);\r\n\r\n  return (\r\n    <>\r\n      <Card\r\n        className=\"dashboard-card\"\r\n        style={{\r\n          borderRadius: 12,\r\n          boxShadow: \"0 2px 8px rgba(0,0,0,0.05)\",\r\n          border: \"none\",\r\n          background: \"linear-gradient(145deg, #ffffff, #f0f7ff)\",\r\n          position: \"relative\",\r\n        }}\r\n      >\r\n        {/* 设置按钮和退出登录按钮容器 */}\r\n        <Flex\r\n          gap={2}\r\n          align=\"center\"\r\n          style={{\r\n            position: \"absolute\",\r\n            top: 12,\r\n            right: 12,\r\n            zIndex: 1,\r\n          }}\r\n        >\r\n          <Dropdown\r\n            overlay={\r\n              <Menu>\r\n                <Menu.Item\r\n                  key=\"editProfile\"\r\n                  icon={<EditOutlined />}\r\n                  onClick={() => {\r\n                    setEditProfileModalVisible(true);\r\n                    setCurrentStep(0);\r\n                    editProfileForm.setFieldsValue({\r\n                      name: userInfo.name,\r\n                      email: userInfo.email,\r\n                      phone: userInfo.phone,\r\n                    });\r\n                  }}\r\n                >\r\n                  修改资料\r\n                </Menu.Item>\r\n                <Menu.Item\r\n                  key=\"subscription\"\r\n                  icon={<TagOutlined />}\r\n                  onClick={() => setSubscriptionModalVisible(true)}\r\n                >\r\n                  订阅套餐\r\n                </Menu.Item>\r\n              </Menu>\r\n            }\r\n            trigger={[\"click\"]}\r\n          >\r\n            <Button\r\n              type=\"text\"\r\n              icon={\r\n                <SettingOutlined style={{ fontSize: 16, color: \"#8c8c8c\" }} />\r\n              }\r\n              style={{ padding: \"4px 6px\" }}\r\n            />\r\n          </Dropdown>\r\n\r\n          {/* 退出登录按钮 */}\r\n          <Button\r\n            type=\"text\"\r\n            icon={<LogoutOutlined style={{ fontSize: 16, color: \"#8c8c8c\" }} />}\r\n            onClick={() => {\r\n              console.log(\"执行退出登录操作\");\r\n              // 实际项目中这里会调用退出登录API\r\n            }}\r\n            style={{ padding: \"4px 6px\" }}\r\n          />\r\n        </Flex>\r\n\r\n        {/* 三列布局 */}\r\n        <Row gutter={0} style={{ margin: 0 }}>\r\n          {/* 第一列：姓名、头像和邮箱等信息 */}\r\n          <Col xs={24} sm={8}>\r\n            <Flex align=\"center\">\r\n              <Avatar\r\n                size={52}\r\n                style={{\r\n                  backgroundColor: \"#1890ff\",\r\n                  fontSize: 18,\r\n                  boxShadow: \"0 0 0 3px rgba(24, 144, 255, 0.2)\",\r\n                  marginRight: 10,\r\n                }}\r\n              >\r\n                {userInfo.name.substring(0, 1)}\r\n              </Avatar>\r\n\r\n              <Space direction=\"vertical\" size={0}>\r\n                <Flex align=\"center\" style={{ marginBottom: 4 }}>\r\n                  <Title level={4} style={{ margin: 0, fontSize: 16 }}>\r\n                    {userInfo.name}\r\n                  </Title>\r\n                  <Tooltip title=\"最后登录时间\">\r\n                    <Text\r\n                      type=\"secondary\"\r\n                      style={{ marginLeft: 8, fontSize: 11 }}\r\n                    >\r\n                      <CalendarOutlined style={{ marginRight: 2 }} />\r\n                      {userInfo.lastLoginTime}\r\n                    </Text>\r\n                  </Tooltip>\r\n                </Flex>\r\n                {/* 邮箱、电话和注册日期信息 */}\r\n                <Flex wrap=\"wrap\" gap={6}>\r\n                  <Tag\r\n                    icon={<MailOutlined style={{ fontSize: 12 }} />}\r\n                    style={{ fontSize: 13 }}\r\n                  >\r\n                    {userInfo.email}\r\n                  </Tag>\r\n                  <Tag\r\n                    icon={<PhoneOutlined style={{ fontSize: 12 }} />}\r\n                    style={{ fontSize: 13 }}\r\n                  >\r\n                    {userInfo.phone}\r\n                  </Tag>\r\n                  <Tag\r\n                    icon={<CalendarOutlined style={{ fontSize: 12 }} />}\r\n                    style={{ fontSize: 13 }}\r\n                  >\r\n                    {userInfo.registerDate}\r\n                  </Tag>\r\n                </Flex>\r\n              </Space>\r\n            </Flex>\r\n          </Col>\r\n\r\n          {/* 第二列：车辆人员预警告警统计 */}\r\n          <Col xs={24} sm={10}>\r\n            {statsError ? (\r\n              <Alert\r\n                message=\"统计数据加载失败\"\r\n                description={statsError}\r\n                type=\"error\"\r\n                showIcon\r\n                style={{ marginBottom: 16 }}\r\n              />\r\n            ) : (\r\n              <Spin spinning={statsLoading}>\r\n                <Row gutter={8} justify=\"space-around\">\r\n                  {/* 车辆统计 */}\r\n                  <Col>\r\n                    <Space direction=\"vertical\">\r\n                      <Flex\r\n                        align=\"center\"\r\n                        style={{ color: \"#595959\", fontSize: 12 }}\r\n                      >\r\n                        <CarOutlined />\r\n                        <Text style={{ marginLeft: 4 }}>车辆</Text>\r\n                      </Flex>\r\n                      <Text\r\n                        strong\r\n                        style={{\r\n                          fontSize: 24,\r\n                          color: \"#1890ff\",\r\n                          fontWeight: 600,\r\n                          lineHeight: \"28px\",\r\n                        }}\r\n                      >\r\n                        {personalStats.vehicles}\r\n                      </Text>\r\n                    </Space>\r\n                  </Col>\r\n\r\n                  {/* 人员统计 */}\r\n                  <Col>\r\n                    <Space direction=\"vertical\">\r\n                      <Flex\r\n                        align=\"center\"\r\n                        style={{ color: \"#595959\", fontSize: 12 }}\r\n                      >\r\n                        <UserOutlined />\r\n                        <Text style={{ marginLeft: 4 }}>人员</Text>\r\n                      </Flex>\r\n                      <Text\r\n                        strong\r\n                        style={{\r\n                          fontSize: 24,\r\n                          color: \"#52c41a\",\r\n                          fontWeight: 600,\r\n                          lineHeight: \"28px\",\r\n                        }}\r\n                      >\r\n                        {personalStats.personnel}\r\n                      </Text>\r\n                    </Space>\r\n                  </Col>\r\n\r\n                  {/* 预警统计 */}\r\n                  <Col>\r\n                    <Space direction=\"vertical\">\r\n                      <Flex\r\n                        align=\"center\"\r\n                        style={{ color: \"#595959\", fontSize: 12 }}\r\n                      >\r\n                        <WarningOutlined />\r\n                        <Text style={{ marginLeft: 4 }}>预警</Text>\r\n                      </Flex>\r\n                      <Text\r\n                        strong\r\n                        style={{\r\n                          fontSize: 24,\r\n                          color: \"#faad14\",\r\n                          fontWeight: 600,\r\n                          lineHeight: \"28px\",\r\n                        }}\r\n                      >\r\n                        {personalStats.warnings}\r\n                      </Text>\r\n                    </Space>\r\n                  </Col>\r\n\r\n                  {/* 告警统计 */}\r\n                  <Col>\r\n                    <Space direction=\"vertical\">\r\n                      <Flex\r\n                        align=\"center\"\r\n                        style={{ color: \"#595959\", fontSize: 12 }}\r\n                      >\r\n                        <WarningOutlined style={{ color: \"#ff4d4f\" }} />\r\n                        <Text style={{ marginLeft: 4 }}>告警</Text>\r\n                      </Flex>\r\n                      <Text\r\n                        strong\r\n                        style={{\r\n                          fontSize: 24,\r\n                          color: \"#ff4d4f\",\r\n                          fontWeight: 600,\r\n                          lineHeight: \"28px\",\r\n                        }}\r\n                      >\r\n                        {personalStats.alerts}\r\n                      </Text>\r\n                    </Space>\r\n                  </Col>\r\n                </Row>\r\n              </Spin>\r\n            )}\r\n          </Col>\r\n\r\n          {/* 第三列：最后登录信息 */}\r\n          <Col xs={24} sm={6}>\r\n            <Space direction=\"vertical\">\r\n              <Flex align=\"center\">\r\n                <TeamOutlined\r\n                  style={{ color: \"#8c8c8c\", marginRight: 6, fontSize: 12 }}\r\n                />\r\n                <Text type=\"secondary\" style={{ fontSize: 12 }}>\r\n                  最后登录团队: <Text strong>{userInfo.lastLoginTeam}</Text>\r\n                </Text>\r\n              </Flex>\r\n              <Flex align=\"center\">\r\n                <TeamOutlined\r\n                  style={{ color: \"#8c8c8c\", marginRight: 6, fontSize: 12 }}\r\n                />\r\n                <Text type=\"secondary\" style={{ fontSize: 12 }}>\r\n                  团队总数: <Text strong>{userInfo.teamCount}</Text>\r\n                </Text>\r\n              </Flex>\r\n            </Space>\r\n          </Col>\r\n        </Row>\r\n      </Card>\r\n\r\n      {/* 修改资料模态框 */}\r\n      <Modal\r\n        title=\"修改个人资料\"\r\n        open={editProfileModalVisible}\r\n        onCancel={() => {\r\n          setEditProfileModalVisible(false);\r\n          setCurrentStep(0);\r\n        }}\r\n        footer={[\r\n          currentStep === 1 && (\r\n            <Button key=\"back\" onClick={() => setCurrentStep(0)}>\r\n              上一步\r\n            </Button>\r\n          ),\r\n          <Button\r\n            key=\"submit\"\r\n            type=\"primary\"\r\n            onClick={() => {\r\n              if (currentStep === 0) {\r\n                editProfileForm.validateFields().then(() => {\r\n                  setCurrentStep(1);\r\n                });\r\n              } else {\r\n                editProfileForm.validateFields().then((values) => {\r\n                  console.log(\"个人资料表单值:\", values);\r\n                  // 提交表单，这里简化处理，只输出到控制台\r\n                  setEditProfileModalVisible(false);\r\n                  setCurrentStep(0);\r\n                });\r\n              }\r\n            }}\r\n          >\r\n            {currentStep === 0 ? \"下一步\" : \"确定\"}\r\n          </Button>,\r\n        ]}\r\n      >\r\n        <Steps current={currentStep} style={{ marginBottom: 16 }}>\r\n          <Step title=\"填写信息\" />\r\n          <Step title=\"安全验证\" />\r\n        </Steps>\r\n\r\n        <Form form={editProfileForm} layout=\"vertical\" hideRequiredMark>\r\n          {currentStep === 0 ? (\r\n            <>\r\n              <Form.Item\r\n                name=\"name\"\r\n                label=\"用户名\"\r\n                rules={[{ required: true, message: \"请输入用户名\" }]}\r\n              >\r\n                <Input placeholder=\"请输入用户名\" />\r\n              </Form.Item>\r\n              <Form.Item\r\n                name=\"email\"\r\n                label=\"邮箱\"\r\n                rules={[\r\n                  { required: true, message: \"请输入邮箱地址\" },\r\n                  { type: \"email\", message: \"请输入有效的邮箱地址\" },\r\n                ]}\r\n              >\r\n                <Input placeholder=\"请输入邮箱地址\" />\r\n              </Form.Item>\r\n              <Form.Item\r\n                name=\"phone\"\r\n                label=\"手机号\"\r\n                rules={[\r\n                  { required: true, message: \"请输入手机号\" },\r\n                  { pattern: /^1\\d{10}$/, message: \"请输入有效的手机号\" },\r\n                ]}\r\n              >\r\n                <Input placeholder=\"请输入手机号\" />\r\n              </Form.Item>\r\n            </>\r\n          ) : (\r\n            <div style={{ textAlign: \"center\" }}>\r\n              <div style={{ margin: \"12px 0\", textAlign: \"center\" }}>\r\n                <Text>\r\n                  验证码已发送至您的手机号{\" \"}\r\n                  <Text strong>{editProfileForm.getFieldValue(\"phone\")}</Text>\r\n                </Text>\r\n              </div>\r\n              <Form.Item\r\n                name=\"verificationCode\"\r\n                label=\"验证码\"\r\n                rules={[{ required: true, message: \"请输入验证码\" }]}\r\n              >\r\n                <Input\r\n                  placeholder=\"请输入6位验证码\"\r\n                  maxLength={6}\r\n                  style={{ width: \"50%\", textAlign: \"center\" }}\r\n                />\r\n              </Form.Item>\r\n              <Button type=\"link\" style={{ padding: 0 }}>\r\n                重新发送验证码\r\n              </Button>\r\n            </div>\r\n          )}\r\n        </Form>\r\n      </Modal>\r\n\r\n      {/* 订阅套餐模态框 */}\r\n      <Modal\r\n        title=\"订阅套餐\"\r\n        open={subscriptionModalVisible}\r\n        onCancel={() => setSubscriptionModalVisible(false)}\r\n        footer={null}\r\n        width={800}\r\n      >\r\n        <div\r\n          style={{\r\n            background: \"#f9f9f9\",\r\n            padding: 12,\r\n            borderRadius: 8,\r\n            marginBottom: 16,\r\n          }}\r\n        >\r\n          <Flex justify=\"space-between\" align=\"center\">\r\n            <Text strong>当前套餐: </Text>\r\n            <Tag color=\"green\" style={{ marginLeft: 8, fontSize: 13 }}>\r\n              {\r\n                subscriptionPlans.find(\r\n                  (p) => p.id === currentSubscription.planId\r\n                )?.name\r\n              }\r\n            </Tag>\r\n            <Text type=\"secondary\">\r\n              到期时间: {currentSubscription.expires}\r\n            </Text>\r\n          </Flex>\r\n        </div>\r\n\r\n        <Row gutter={24}>\r\n          {subscriptionPlans.map((plan) => (\r\n            <Col span={8} key={plan.id}>\r\n              <div\r\n                style={{\r\n                  height: \"100%\",\r\n                  borderRadius: 8,\r\n                  border: `1px solid ${\r\n                    plan.id === currentSubscription.planId\r\n                      ? \"#52c41a\"\r\n                      : \"#d9d9d9\"\r\n                  }`,\r\n                  background: \"#fff\",\r\n                  position: \"relative\",\r\n                  overflow: \"hidden\",\r\n                }}\r\n              >\r\n                {plan.id === currentSubscription.planId && (\r\n                  <Tag\r\n                    color=\"green\"\r\n                    style={{\r\n                      position: \"absolute\",\r\n                      top: -10,\r\n                      right: -10,\r\n                      borderRadius: 2,\r\n                      boxShadow: \"0 2px 8px rgba(0,0,0,0.1)\",\r\n                    }}\r\n                  >\r\n                    当前套餐\r\n                  </Tag>\r\n                )}\r\n                <div style={{ padding: 16 }}>\r\n                  <Title\r\n                    level={4}\r\n                    style={{ textAlign: \"center\", margin: \"12px 0 8px\" }}\r\n                  >\r\n                    {plan.name}\r\n                  </Title>\r\n                  <Flex vertical align=\"center\" style={{ marginBottom: 12 }}>\r\n                    {plan.price > 0 ? (\r\n                      <>\r\n                        <Title level={2} style={{ marginBottom: 0 }}>\r\n                          ¥{plan.price}\r\n                        </Title>\r\n                        <Text type=\"secondary\">/月</Text>\r\n                      </>\r\n                    ) : (\r\n                      <Title\r\n                        level={2}\r\n                        style={{ color: \"#52c41a\", marginBottom: 0 }}\r\n                      >\r\n                        免费\r\n                      </Title>\r\n                    )}\r\n                    <Text type=\"secondary\" style={{ marginTop: 4 }}>\r\n                      {plan.description}\r\n                    </Text>\r\n                  </Flex>\r\n\r\n                  <Divider style={{ margin: \"8px 0\" }} />\r\n\r\n                  <div style={{ minHeight: 170 }}>\r\n                    {plan.features.map((feature, index) => (\r\n                      <Space\r\n                        key={index}\r\n                        align=\"start\"\r\n                        style={{ marginBottom: 6 }}\r\n                      >\r\n                        <CheckOutlined\r\n                          style={{\r\n                            color: \"#52c41a\",\r\n                            marginRight: 8,\r\n                            marginTop: 4,\r\n                          }}\r\n                        />\r\n                        <Text>{feature}</Text>\r\n                      </Space>\r\n                    ))}\r\n                  </div>\r\n\r\n                  {plan.id !== currentSubscription.planId ? (\r\n                    <Button\r\n                      type=\"primary\"\r\n                      block\r\n                      style={{\r\n                        marginTop: 12,\r\n                        boxShadow: \"0 2px 8px rgba(24, 144, 255, 0.3)\",\r\n                      }}\r\n                      onClick={() => {\r\n                        console.log(\"选择套餐:\", plan);\r\n                        setSubscriptionModalVisible(false);\r\n                      }}\r\n                    >\r\n                      立即订阅\r\n                    </Button>\r\n                  ) : (\r\n                    <Button\r\n                      block\r\n                      style={{\r\n                        marginTop: 12,\r\n                        background: \"#f6ffed\",\r\n                        borderColor: \"#b7eb8f\",\r\n                        color: \"#389e0d\",\r\n                      }}\r\n                      disabled\r\n                    >\r\n                      当前套餐\r\n                    </Button>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </Col>\r\n          ))}\r\n        </Row>\r\n\r\n        <Flex justify=\"center\" style={{ marginTop: 20 }}>\r\n          <Text type=\"secondary\">订阅服务自动续费，可随时取消</Text>\r\n        </Flex>\r\n      </Modal>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default UserProfileCard;\r\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,iDACA;IACE,SAAS;;;;;;wCCipBb;;;2BAAA;;;;;;0CAvoBO;yCAqBA;oFACoC;yCACf;;;;;;;;;;YAG5B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;YAClC,MAAM,EAAE,IAAI,EAAE,GAAG,WAAK;YAEtB,MAAM,kBAA4B;oBAoelB;;gBAned,SAAS;gBACT,MAAM,WAAW;oBACf,MAAM;oBACN,UAAU;oBACV,OAAO;oBACP,OAAO;oBACP,cAAc;oBACd,eAAe;oBACf,eAAe;oBACf,WAAW;gBACb;gBAEA,WAAW;gBACX,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAA4B;oBAC5E,UAAU;oBACV,WAAW;oBACX,UAAU;oBACV,QAAQ;gBACV;gBACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,eAAQ,EAAC;gBACjD,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAgB;gBAE5D,SAAS;gBACT,MAAM,oBAAoB;oBACxB;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;4BAAC;4BAAU;4BAAW;4BAAU;yBAAS;oBACrD;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;4BACR;4BACA;4BACA;4BACA;4BACA;4BACA;yBACD;oBACH;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;4BACR;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;yBACD;oBACH;iBACD;gBAED,SAAS;gBACT,MAAM,sBAAsB;oBAC1B,QAAQ;oBACR,SAAS;gBACX;gBAEA,OAAO;gBACP,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,IAAA,eAAQ,EAAC;gBACvE,MAAM,CAAC,0BAA0B,4BAA4B,GAC3D,IAAA,eAAQ,EAAC;gBACX,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,eAAQ,EAAC;gBAC/C,MAAM,CAAC,gBAAgB,GAAG,UAAI,CAAC,OAAO;gBAEtC,WAAW;gBACX,IAAA,gBAAS,EAAC;oBACR,QAAQ,GAAG,CAAC;oBACZ,MAAM,qBAAqB;wBACzB,IAAI;4BACF,QAAQ,GAAG,CAAC;4BACZ,gBAAgB;4BAChB,cAAc;4BACd,MAAM,QAAQ,MAAM,iBAAW,CAAC,oBAAoB;4BACpD,QAAQ,GAAG,CAAC,6BAA6B;4BACzC,iBAAiB;wBACnB,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,eAAe;4BAC7B,cAAc;wBAChB,SAAU;4BACR,gBAAgB;wBAClB;oBACF;oBAEA;gBACF,GAAG,EAAE;gBAEL,qBACE;;sCACE,2BAAC,UAAI;4BACH,WAAU;4BACV,OAAO;gCACL,cAAc;gCACd,WAAW;gCACX,QAAQ;gCACR,YAAY;gCACZ,UAAU;4BACZ;;8CAGA,2BAAC,UAAI;oCACH,KAAK;oCACL,OAAM;oCACN,OAAO;wCACL,UAAU;wCACV,KAAK;wCACL,OAAO;wCACP,QAAQ;oCACV;;sDAEA,2BAAC,cAAQ;4CACP,uBACE,2BAAC,UAAI;;kEACH,2BAAC,UAAI,CAAC,IAAI;wDAER,oBAAM,2BAAC,mBAAY;;;;;wDACnB,SAAS;4DACP,2BAA2B;4DAC3B,eAAe;4DACf,gBAAgB,cAAc,CAAC;gEAC7B,MAAM,SAAS,IAAI;gEACnB,OAAO,SAAS,KAAK;gEACrB,OAAO,SAAS,KAAK;4DACvB;wDACF;kEACD;uDAXK;;;;;kEAcN,2BAAC,UAAI,CAAC,IAAI;wDAER,oBAAM,2BAAC,kBAAW;;;;;wDAClB,SAAS,IAAM,4BAA4B;kEAC5C;uDAHK;;;;;;;;;;;4CAQV,SAAS;gDAAC;6CAAQ;sDAElB,cAAA,2BAAC,YAAM;gDACL,MAAK;gDACL,oBACE,2BAAC,sBAAe;oDAAC,OAAO;wDAAE,UAAU;wDAAI,OAAO;oDAAU;;;;;;gDAE3D,OAAO;oDAAE,SAAS;gDAAU;;;;;;;;;;;sDAKhC,2BAAC,YAAM;4CACL,MAAK;4CACL,oBAAM,2BAAC,qBAAc;gDAAC,OAAO;oDAAE,UAAU;oDAAI,OAAO;gDAAU;;;;;;4CAC9D,SAAS;gDACP,QAAQ,GAAG,CAAC;4CACZ,oBAAoB;4CACtB;4CACA,OAAO;gDAAE,SAAS;4CAAU;;;;;;;;;;;;8CAKhC,2BAAC,SAAG;oCAAC,QAAQ;oCAAG,OAAO;wCAAE,QAAQ;oCAAE;;sDAEjC,2BAAC,SAAG;4CAAC,IAAI;4CAAI,IAAI;sDACf,cAAA,2BAAC,UAAI;gDAAC,OAAM;;kEACV,2BAAC,YAAM;wDACL,MAAM;wDACN,OAAO;4DACL,iBAAiB;4DACjB,UAAU;4DACV,WAAW;4DACX,aAAa;wDACf;kEAEC,SAAS,IAAI,CAAC,SAAS,CAAC,GAAG;;;;;;kEAG9B,2BAAC,WAAK;wDAAC,WAAU;wDAAW,MAAM;;0EAChC,2BAAC,UAAI;gEAAC,OAAM;gEAAS,OAAO;oEAAE,cAAc;gEAAE;;kFAC5C,2BAAC;wEAAM,OAAO;wEAAG,OAAO;4EAAE,QAAQ;4EAAG,UAAU;wEAAG;kFAC/C,SAAS,IAAI;;;;;;kFAEhB,2BAAC,aAAO;wEAAC,OAAM;kFACb,cAAA,2BAAC;4EACC,MAAK;4EACL,OAAO;gFAAE,YAAY;gFAAG,UAAU;4EAAG;;8FAErC,2BAAC,uBAAgB;oFAAC,OAAO;wFAAE,aAAa;oFAAE;;;;;;gFACzC,SAAS,aAAa;;;;;;;;;;;;;;;;;;0EAK7B,2BAAC,UAAI;gEAAC,MAAK;gEAAO,KAAK;;kFACrB,2BAAC,SAAG;wEACF,oBAAM,2BAAC,mBAAY;4EAAC,OAAO;gFAAE,UAAU;4EAAG;;;;;;wEAC1C,OAAO;4EAAE,UAAU;wEAAG;kFAErB,SAAS,KAAK;;;;;;kFAEjB,2BAAC,SAAG;wEACF,oBAAM,2BAAC,oBAAa;4EAAC,OAAO;gFAAE,UAAU;4EAAG;;;;;;wEAC3C,OAAO;4EAAE,UAAU;wEAAG;kFAErB,SAAS,KAAK;;;;;;kFAEjB,2BAAC,SAAG;wEACF,oBAAM,2BAAC,uBAAgB;4EAAC,OAAO;gFAAE,UAAU;4EAAG;;;;;;wEAC9C,OAAO;4EAAE,UAAU;wEAAG;kFAErB,SAAS,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAQhC,2BAAC,SAAG;4CAAC,IAAI;4CAAI,IAAI;sDACd,2BACC,2BAAC,WAAK;gDACJ,SAAQ;gDACR,aAAa;gDACb,MAAK;gDACL,QAAQ;gDACR,OAAO;oDAAE,cAAc;gDAAG;;;;;qEAG5B,2BAAC,UAAI;gDAAC,UAAU;0DACd,cAAA,2BAAC,SAAG;oDAAC,QAAQ;oDAAG,SAAQ;;sEAEtB,2BAAC,SAAG;sEACF,cAAA,2BAAC,WAAK;gEAAC,WAAU;;kFACf,2BAAC,UAAI;wEACH,OAAM;wEACN,OAAO;4EAAE,OAAO;4EAAW,UAAU;wEAAG;;0FAExC,2BAAC,kBAAW;;;;;0FACZ,2BAAC;gFAAK,OAAO;oFAAE,YAAY;gFAAE;0FAAG;;;;;;;;;;;;kFAElC,2BAAC;wEACC,MAAM;wEACN,OAAO;4EACL,UAAU;4EACV,OAAO;4EACP,YAAY;4EACZ,YAAY;wEACd;kFAEC,cAAc,QAAQ;;;;;;;;;;;;;;;;;sEAM7B,2BAAC,SAAG;sEACF,cAAA,2BAAC,WAAK;gEAAC,WAAU;;kFACf,2BAAC,UAAI;wEACH,OAAM;wEACN,OAAO;4EAAE,OAAO;4EAAW,UAAU;wEAAG;;0FAExC,2BAAC,mBAAY;;;;;0FACb,2BAAC;gFAAK,OAAO;oFAAE,YAAY;gFAAE;0FAAG;;;;;;;;;;;;kFAElC,2BAAC;wEACC,MAAM;wEACN,OAAO;4EACL,UAAU;4EACV,OAAO;4EACP,YAAY;4EACZ,YAAY;wEACd;kFAEC,cAAc,SAAS;;;;;;;;;;;;;;;;;sEAM9B,2BAAC,SAAG;sEACF,cAAA,2BAAC,WAAK;gEAAC,WAAU;;kFACf,2BAAC,UAAI;wEACH,OAAM;wEACN,OAAO;4EAAE,OAAO;4EAAW,UAAU;wEAAG;;0FAExC,2BAAC,sBAAe;;;;;0FAChB,2BAAC;gFAAK,OAAO;oFAAE,YAAY;gFAAE;0FAAG;;;;;;;;;;;;kFAElC,2BAAC;wEACC,MAAM;wEACN,OAAO;4EACL,UAAU;4EACV,OAAO;4EACP,YAAY;4EACZ,YAAY;wEACd;kFAEC,cAAc,QAAQ;;;;;;;;;;;;;;;;;sEAM7B,2BAAC,SAAG;sEACF,cAAA,2BAAC,WAAK;gEAAC,WAAU;;kFACf,2BAAC,UAAI;wEACH,OAAM;wEACN,OAAO;4EAAE,OAAO;4EAAW,UAAU;wEAAG;;0FAExC,2BAAC,sBAAe;gFAAC,OAAO;oFAAE,OAAO;gFAAU;;;;;;0FAC3C,2BAAC;gFAAK,OAAO;oFAAE,YAAY;gFAAE;0FAAG;;;;;;;;;;;;kFAElC,2BAAC;wEACC,MAAM;wEACN,OAAO;4EACL,UAAU;4EACV,OAAO;4EACP,YAAY;4EACZ,YAAY;wEACd;kFAEC,cAAc,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAUnC,2BAAC,SAAG;4CAAC,IAAI;4CAAI,IAAI;sDACf,cAAA,2BAAC,WAAK;gDAAC,WAAU;;kEACf,2BAAC,UAAI;wDAAC,OAAM;;0EACV,2BAAC,mBAAY;gEACX,OAAO;oEAAE,OAAO;oEAAW,aAAa;oEAAG,UAAU;gEAAG;;;;;;0EAE1D,2BAAC;gEAAK,MAAK;gEAAY,OAAO;oEAAE,UAAU;gEAAG;;oEAAG;kFACtC,2BAAC;wEAAK,MAAM;kFAAE,SAAS,aAAa;;;;;;;;;;;;;;;;;;kEAGhD,2BAAC,UAAI;wDAAC,OAAM;;0EACV,2BAAC,mBAAY;gEACX,OAAO;oEAAE,OAAO;oEAAW,aAAa;oEAAG,UAAU;gEAAG;;;;;;0EAE1D,2BAAC;gEAAK,MAAK;gEAAY,OAAO;oEAAE,UAAU;gEAAG;;oEAAG;kFACxC,2BAAC;wEAAK,MAAM;kFAAE,SAAS,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASlD,2BAAC,WAAK;4BACJ,OAAM;4BACN,MAAM;4BACN,UAAU;gCACR,2BAA2B;gCAC3B,eAAe;4BACjB;4BACA,QAAQ;gCACN,gBAAgB,mBACd,2BAAC,YAAM;oCAAY,SAAS,IAAM,eAAe;8CAAI;mCAAzC;;;;;8CAId,2BAAC,YAAM;oCAEL,MAAK;oCACL,SAAS;wCACP,IAAI,gBAAgB,GAClB,gBAAgB,cAAc,GAAG,IAAI,CAAC;4CACpC,eAAe;wCACjB;6CAEA,gBAAgB,cAAc,GAAG,IAAI,CAAC,CAAC;4CACrC,QAAQ,GAAG,CAAC,YAAY;4CACxB,sBAAsB;4CACtB,2BAA2B;4CAC3B,eAAe;wCACjB;oCAEJ;8CAEC,gBAAgB,IAAI,QAAQ;mCAjBzB;;;;;6BAmBP;;8CAED,2BAAC,WAAK;oCAAC,SAAS;oCAAa,OAAO;wCAAE,cAAc;oCAAG;;sDACrD,2BAAC;4CAAK,OAAM;;;;;;sDACZ,2BAAC;4CAAK,OAAM;;;;;;;;;;;;8CAGd,2BAAC,UAAI;oCAAC,MAAM;oCAAiB,QAAO;oCAAW,gBAAgB;8CAC5D,gBAAgB,kBACf;;0DACE,2BAAC,UAAI,CAAC,IAAI;gDACR,MAAK;gDACL,OAAM;gDACN,OAAO;oDAAC;wDAAE,UAAU;wDAAM,SAAS;oDAAS;iDAAE;0DAE9C,cAAA,2BAAC,WAAK;oDAAC,aAAY;;;;;;;;;;;0DAErB,2BAAC,UAAI,CAAC,IAAI;gDACR,MAAK;gDACL,OAAM;gDACN,OAAO;oDACL;wDAAE,UAAU;wDAAM,SAAS;oDAAU;oDACrC;wDAAE,MAAM;wDAAS,SAAS;oDAAa;iDACxC;0DAED,cAAA,2BAAC,WAAK;oDAAC,aAAY;;;;;;;;;;;0DAErB,2BAAC,UAAI,CAAC,IAAI;gDACR,MAAK;gDACL,OAAM;gDACN,OAAO;oDACL;wDAAE,UAAU;wDAAM,SAAS;oDAAS;oDACpC;wDAAE,SAAS;wDAAa,SAAS;oDAAY;iDAC9C;0DAED,cAAA,2BAAC,WAAK;oDAAC,aAAY;;;;;;;;;;;;qEAIvB,2BAAC;wCAAI,OAAO;4CAAE,WAAW;wCAAS;;0DAChC,2BAAC;gDAAI,OAAO;oDAAE,QAAQ;oDAAU,WAAW;gDAAS;0DAClD,cAAA,2BAAC;;wDAAK;wDACS;sEACb,2BAAC;4DAAK,MAAM;sEAAE,gBAAgB,aAAa,CAAC;;;;;;;;;;;;;;;;;0DAGhD,2BAAC,UAAI,CAAC,IAAI;gDACR,MAAK;gDACL,OAAM;gDACN,OAAO;oDAAC;wDAAE,UAAU;wDAAM,SAAS;oDAAS;iDAAE;0DAE9C,cAAA,2BAAC,WAAK;oDACJ,aAAY;oDACZ,WAAW;oDACX,OAAO;wDAAE,OAAO;wDAAO,WAAW;oDAAS;;;;;;;;;;;0DAG/C,2BAAC,YAAM;gDAAC,MAAK;gDAAO,OAAO;oDAAE,SAAS;gDAAE;0DAAG;;;;;;;;;;;;;;;;;;;;;;;sCASnD,2BAAC,WAAK;4BACJ,OAAM;4BACN,MAAM;4BACN,UAAU,IAAM,4BAA4B;4BAC5C,QAAQ;4BACR,OAAO;;8CAEP,2BAAC;oCACC,OAAO;wCACL,YAAY;wCACZ,SAAS;wCACT,cAAc;wCACd,cAAc;oCAChB;8CAEA,cAAA,2BAAC,UAAI;wCAAC,SAAQ;wCAAgB,OAAM;;0DAClC,2BAAC;gDAAK,MAAM;0DAAC;;;;;;0DACb,2BAAC,SAAG;gDAAC,OAAM;gDAAQ,OAAO;oDAAE,YAAY;oDAAG,UAAU;gDAAG;2DAEpD,0BAAA,kBAAkB,IAAI,CACpB,CAAC,IAAM,EAAE,EAAE,KAAK,oBAAoB,MAAM,eAD5C,8CAAA,wBAEG,IAAI;;;;;;0DAGX,2BAAC;gDAAK,MAAK;;oDAAY;oDACd,oBAAoB,OAAO;;;;;;;;;;;;;;;;;;8CAKxC,2BAAC,SAAG;oCAAC,QAAQ;8CACV,kBAAkB,GAAG,CAAC,CAAC,qBACtB,2BAAC,SAAG;4CAAC,MAAM;sDACT,cAAA,2BAAC;gDACC,OAAO;oDACL,QAAQ;oDACR,cAAc;oDACd,QAAQ,CAAC,UAAU,EACjB,KAAK,EAAE,KAAK,oBAAoB,MAAM,GAClC,YACA,UACL,CAAC;oDACF,YAAY;oDACZ,UAAU;oDACV,UAAU;gDACZ;;oDAEC,KAAK,EAAE,KAAK,oBAAoB,MAAM,kBACrC,2BAAC,SAAG;wDACF,OAAM;wDACN,OAAO;4DACL,UAAU;4DACV,KAAK;4DACL,OAAO;4DACP,cAAc;4DACd,WAAW;wDACb;kEACD;;;;;;kEAIH,2BAAC;wDAAI,OAAO;4DAAE,SAAS;wDAAG;;0EACxB,2BAAC;gEACC,OAAO;gEACP,OAAO;oEAAE,WAAW;oEAAU,QAAQ;gEAAa;0EAElD,KAAK,IAAI;;;;;;0EAEZ,2BAAC,UAAI;gEAAC,QAAQ;gEAAC,OAAM;gEAAS,OAAO;oEAAE,cAAc;gEAAG;;oEACrD,KAAK,KAAK,GAAG,kBACZ;;0FACE,2BAAC;gFAAM,OAAO;gFAAG,OAAO;oFAAE,cAAc;gFAAE;;oFAAG;oFACzC,KAAK,KAAK;;;;;;;0FAEd,2BAAC;gFAAK,MAAK;0FAAY;;;;;;;qGAGzB,2BAAC;wEACC,OAAO;wEACP,OAAO;4EAAE,OAAO;4EAAW,cAAc;wEAAE;kFAC5C;;;;;;kFAIH,2BAAC;wEAAK,MAAK;wEAAY,OAAO;4EAAE,WAAW;wEAAE;kFAC1C,KAAK,WAAW;;;;;;;;;;;;0EAIrB,2BAAC,aAAO;gEAAC,OAAO;oEAAE,QAAQ;gEAAQ;;;;;;0EAElC,2BAAC;gEAAI,OAAO;oEAAE,WAAW;gEAAI;0EAC1B,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC3B,2BAAC,WAAK;wEAEJ,OAAM;wEACN,OAAO;4EAAE,cAAc;wEAAE;;0FAEzB,2BAAC,oBAAa;gFACZ,OAAO;oFACL,OAAO;oFACP,aAAa;oFACb,WAAW;gFACb;;;;;;0FAEF,2BAAC;0FAAM;;;;;;;uEAXF;;;;;;;;;;4DAgBV,KAAK,EAAE,KAAK,oBAAoB,MAAM,iBACrC,2BAAC,YAAM;gEACL,MAAK;gEACL,KAAK;gEACL,OAAO;oEACL,WAAW;oEACX,WAAW;gEACb;gEACA,SAAS;oEACP,QAAQ,GAAG,CAAC,SAAS;oEACrB,4BAA4B;gEAC9B;0EACD;;;;;qFAID,2BAAC,YAAM;gEACL,KAAK;gEACL,OAAO;oEACL,WAAW;oEACX,YAAY;oEACZ,aAAa;oEACb,OAAO;gEACT;gEACA,QAAQ;0EACT;;;;;;;;;;;;;;;;;;2CAvGU,KAAK,EAAE;;;;;;;;;;8CAiH9B,2BAAC,UAAI;oCAAC,SAAQ;oCAAS,OAAO;wCAAE,WAAW;oCAAG;8CAC5C,cAAA,2BAAC;wCAAK,MAAK;kDAAY;;;;;;;;;;;;;;;;;;;YAKjC;eAxmBM;;oBA0EsB,UAAI,CAAC;;;iBA1E3B;gBA0mBN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;IDjpBD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,uCAAsC;YAAC;YAAU;SAAsC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,8BAA6B;YAAC;SAAmB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;YAAS;SAAyB;QAAC,mCAAkC;YAAC;YAAS;SAAyB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,iDAAgD;YAAC;SAAgD;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,kCAAiC;YAAC;SAAwB;QAAC,wCAAuC;YAAC;SAAuC;IAAA;;AACh/B"}