{"version": 3, "sources": ["src_pages_test_profile-consolidated_index_tsx-async.7582035084250787357.hot-update.js", "src/pages/test/profile-consolidated/TodoManagement.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/test/profile-consolidated/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='12002734723833457591';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/.umi/plugin-openapi/openapi.tsx\":[\"vendors\",\"src/.umi/plugin-openapi/openapi.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/friend/index.tsx\":[\"p__friend__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"common\",\"p__subscription__index\"],\"src/pages/team/detail/index.tsx\":[\"common\",\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"p__team__index\"],\"src/pages/test/profile-consolidated/index.tsx\":[\"src/pages/test/profile-consolidated/index.tsx\"],\"src/pages/user/index.tsx\":[\"common\",\"p__user__index\"],\"src/pages/user/login/index.tsx\":[\"p__user__login__index\"],\"src/pages/user/team-select/index.tsx\":[\"src/pages/user/team-select/index.tsx\"]});;\r\n  },\r\n);\r\n", "import React, { useState, useEffect } from \"react\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  Card,\r\n  Divider,\r\n  Dropdown,\r\n  Flex,\r\n  Form,\r\n  Input,\r\n  List,\r\n  Modal,\r\n  Progress,\r\n  Select,\r\n  Space,\r\n  Tabs,\r\n  Tag,\r\n  Tooltip,\r\n  Typography,\r\n  Spin,\r\n  Alert,\r\n  message,\r\n} from \"antd\";\r\nimport { TodoService } from \"@/services/todo\";\r\nimport type { TodoResponse, TodoStatsResponse } from \"@/types/api\";\r\nimport {\r\n  CheckOutlined,\r\n  DeleteOutlined,\r\n  EditOutlined,\r\n  MoreOutlined,\r\n  PlusOutlined,\r\n  SearchOutlined,\r\n  CalendarOutlined,\r\n} from \"@ant-design/icons\";\r\n\r\nconst { Text } = Typography;\r\nconst { TabPane } = Tabs;\r\n\r\n// 使用API类型定义，不需要重复定义接口\r\ninterface TodoManagementProps {\r\n  onAddTodo?: (todo: TodoResponse) => void;\r\n  onUpdateTodo?: (id: number, updatedTodo: Partial<TodoResponse>) => void;\r\n  onDeleteTodo?: (id: number) => void;\r\n}\r\n\r\nconst TodoManagement: React.FC<TodoManagementProps> = (props) => {\r\n  // TODO数据状态管理\r\n  const [personalTasks, setPersonalTasks] = useState<TodoResponse[]>([]);\r\n  const [todoStats, setTodoStats] = useState<TodoStatsResponse>({\r\n    highPriorityCount: 0,\r\n    mediumPriorityCount: 0,\r\n    lowPriorityCount: 0,\r\n    totalCount: 0,\r\n    completedCount: 0,\r\n    completionPercentage: 0,\r\n  });\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  // 待办事项状态管理\r\n  const [todoModalVisible, setTodoModalVisible] = useState(false);\r\n  const [todoForm] = Form.useForm();\r\n  const [editingTodoId, setEditingTodoId] = useState<number | null>(null);\r\n\r\n  // 过滤器状态\r\n  const [activeTab, setActiveTab] = useState<\"all\" | \"pending\" | \"completed\">(\r\n    \"pending\"\r\n  );\r\n  const [searchText, setSearchText] = useState(\"\");\r\n\r\n  // 获取TODO数据\r\n  useEffect(() => {\r\n    const fetchTodoData = async () => {\r\n      try {\r\n        setLoading(true);\r\n        setError(null);\r\n\r\n        const [todos, stats] = await Promise.all([\r\n          TodoService.getUserTodos(),\r\n          TodoService.getTodoStats()\r\n        ]);\r\n\r\n        setPersonalTasks(todos);\r\n        setTodoStats(stats);\r\n      } catch (error) {\r\n        console.error('获取TODO数据失败:', error);\r\n        setError('获取TODO数据失败');\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchTodoData();\r\n  }, []);\r\n\r\n  // 根据激活的标签和搜索文本过滤任务\r\n  const filteredPersonalTasks = personalTasks.filter((task) => {\r\n    // 根据标签过滤\r\n    if (activeTab === \"pending\" && task.status === 1) return false;\r\n    if (activeTab === \"completed\" && task.status === 0) return false;\r\n\r\n    // 根据搜索文本过滤\r\n    if (\r\n      searchText &&\r\n      !task.title.toLowerCase().includes(searchText.toLowerCase())\r\n    ) {\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  });\r\n\r\n  // 处理待办事项操作\r\n  const handleToggleTodoStatus = async (id: number) => {\r\n    try {\r\n      const task = personalTasks.find(t => t.id === id);\r\n      if (!task) return;\r\n\r\n      const newStatus = task.status === 0 ? 1 : 0;\r\n      await TodoService.updateTodo(id, { status: newStatus });\r\n\r\n      // 更新本地状态\r\n      setPersonalTasks(\r\n        personalTasks.map((task) =>\r\n          task.id === id ? { ...task, status: newStatus } : task\r\n        )\r\n      );\r\n\r\n      // 刷新统计数据\r\n      const stats = await TodoService.getTodoStats();\r\n      setTodoStats(stats);\r\n\r\n      message.success(newStatus === 1 ? '任务已完成' : '任务已标记为未完成');\r\n    } catch (error) {\r\n      console.error('更新任务状态失败:', error);\r\n      message.error('更新任务状态失败');\r\n    }\r\n  };\r\n\r\n  const handleAddOrUpdateTodo = async (values: any) => {\r\n    try {\r\n      if (editingTodoId) {\r\n        // 更新现有待办事项\r\n        const updatedTodo = await TodoService.updateTodo(editingTodoId, {\r\n          title: values.name,\r\n          priority: values.priority\r\n        });\r\n\r\n        setPersonalTasks(\r\n          personalTasks.map((task) =>\r\n            task.id === editingTodoId ? updatedTodo : task\r\n          )\r\n        );\r\n        message.success('任务更新成功');\r\n      } else {\r\n        // 添加新待办事项\r\n        const newTodo = await TodoService.createTodo({\r\n          title: values.name,\r\n          priority: values.priority\r\n        });\r\n\r\n        setPersonalTasks([newTodo, ...personalTasks]);\r\n        message.success('任务创建成功');\r\n      }\r\n\r\n      // 刷新统计数据\r\n      const stats = await TodoService.getTodoStats();\r\n      setTodoStats(stats);\r\n\r\n      // 重置表单并关闭模态框\r\n      setTodoModalVisible(false);\r\n      setEditingTodoId(null);\r\n      todoForm.resetFields();\r\n    } catch (error) {\r\n      console.error('保存任务失败:', error);\r\n      message.error('保存任务失败');\r\n    }\r\n  };\r\n\r\n  const handleDeleteTodo = async (id: number) => {\r\n    try {\r\n      await TodoService.deleteTodo(id);\r\n      setPersonalTasks(personalTasks.filter((task) => task.id !== id));\r\n\r\n      // 刷新统计数据\r\n      const stats = await TodoService.getTodoStats();\r\n      setTodoStats(stats);\r\n\r\n      message.success('任务删除成功');\r\n    } catch (error) {\r\n      console.error('删除任务失败:', error);\r\n      message.error('删除任务失败');\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Card\r\n      className=\"dashboard-card\"\r\n      style={{\r\n        borderRadius: 12,\r\n        boxShadow: \"0 4px 12px rgba(0,0,0,0.05)\",\r\n        border: \"none\",\r\n        background: \"linear-gradient(145deg, #ffffff, #f5f8ff)\",\r\n      }}\r\n      title={\r\n        <Flex justify=\"space-between\" align=\"center\">\r\n          <Text strong>待办事项</Text>\r\n        </Flex>\r\n      }\r\n    >\r\n      {/* 第一行：搜索框、新增任务按钮、优先级状态点和进度条 */}\r\n      <Flex\r\n        justify=\"space-between\"\r\n        align=\"center\"\r\n        style={{ marginBottom: 16, gap: 16, flexWrap: \"wrap\" }}\r\n      >\r\n        <Space size={16} style={{ flex: 1, minWidth: 300 }}>\r\n          {/* 搜索框 */}\r\n          <Input.Search\r\n            placeholder=\"搜索任务...\"\r\n            allowClear\r\n            prefix={<SearchOutlined />}\r\n            value={searchText}\r\n            onChange={(e) => setSearchText(e.target.value)}\r\n            style={{ flex: 1 }}\r\n          />\r\n\r\n          {/* 新增任务按钮 */}\r\n          <Button\r\n            type=\"primary\"\r\n            icon={<PlusOutlined />}\r\n            onClick={() => {\r\n              setEditingTodoId(null);\r\n              todoForm.resetFields();\r\n              setTodoModalVisible(true);\r\n            }}\r\n            style={{\r\n              background: \"#1890ff\",\r\n              borderColor: \"#1890ff\",\r\n              boxShadow: \"0 2px 4px rgba(24, 144, 255, 0.3)\",\r\n              fontWeight: 500,\r\n            }}\r\n          >\r\n            新增\r\n          </Button>\r\n        </Space>\r\n        \r\n        <Divider type=\"vertical\" style={{ height: 20, backgroundColor: \"#e8e8e8\" }} />\r\n        \r\n        <Space align=\"center\" size={16}>\r\n          {/* 优先级状态点 */}\r\n          <Space size={8}>\r\n            <Tooltip title=\"高优先级任务\">\r\n              <Flex align=\"center\">\r\n                <div\r\n                  style={{\r\n                    width: 10,\r\n                    height: 10,\r\n                    borderRadius: \"50%\",\r\n                    background: \"#ff4d4f\",\r\n                    marginRight: 4,\r\n                  }}\r\n                />\r\n                <Text style={{ fontSize: 13, fontWeight: 500 }}>\r\n                  1000\r\n                </Text>\r\n              </Flex>\r\n            </Tooltip>\r\n\r\n            <Tooltip title=\"中优先级任务\">\r\n              <Flex align=\"center\">\r\n                <div\r\n                  style={{\r\n                    width: 10,\r\n                    height: 10,\r\n                    borderRadius: \"50%\",\r\n                    background: \"#faad14\",\r\n                    marginRight: 4,\r\n                  }}\r\n                />\r\n                <Text style={{ fontSize: 13, fontWeight: 500 }}>\r\n                  10000\r\n                </Text>\r\n              </Flex>\r\n            </Tooltip>\r\n\r\n            <Tooltip title=\"低优先级任务\">\r\n              <Flex align=\"center\">\r\n                <div\r\n                  style={{\r\n                    width: 10,\r\n                    height: 10,\r\n                    borderRadius: \"50%\",\r\n                    background: \"#52c41a\",\r\n                    marginRight: 4,\r\n                  }}\r\n                />\r\n                <Text style={{ fontSize: 13, fontWeight: 500 }}>\r\n                  10000\r\n                </Text>\r\n              </Flex>\r\n            </Tooltip>\r\n          </Space>\r\n\r\n          <Divider type=\"vertical\" style={{ height: 20, backgroundColor: \"#e8e8e8\" }} />\r\n\r\n          {/* 进度条 */}\r\n          <Tooltip title=\"任务完成进度\">\r\n            <Progress\r\n              percent={completionPercentage}\r\n              size=\"small\"\r\n              style={{ width: 120 }}\r\n              strokeColor=\"#52c41a\"\r\n            />\r\n          </Tooltip>\r\n        </Space>\r\n      </Flex>\r\n\r\n      {/* 第二行：标签页 */}\r\n      <Tabs\r\n        activeKey={activeTab}\r\n        onChange={(key) =>\r\n          setActiveTab(key as \"all\" | \"pending\" | \"completed\")\r\n        }\r\n        size=\"middle\"\r\n        style={{ marginBottom: 8 }}\r\n      >\r\n        <TabPane tab=\"全部\" key=\"all\" />\r\n        <TabPane tab=\"待处理\" key=\"pending\" />\r\n        <TabPane tab=\"已完成\" key=\"completed\" />\r\n      </Tabs>\r\n\r\n      {/* 待办事项列表 */}\r\n      <List\r\n        dataSource={filteredPersonalTasks}\r\n        renderItem={(item) => {\r\n          return (\r\n            <List.Item\r\n              className=\"todo-item\"\r\n              style={{\r\n                padding: \"10px 16px\",\r\n                marginBottom: 12,\r\n                borderRadius: 8,\r\n                background: \"#fff\",\r\n                opacity: item.completed ? 0.7 : 1,\r\n                borderLeft: `3px solid ${\r\n                  item.completed\r\n                    ? \"#52c41a\"\r\n                    : item.priority === \"high\"\r\n                    ? \"#ff4d4f\"\r\n                    : item.priority === \"medium\"\r\n                    ? \"#faad14\"\r\n                    : \"#8c8c8c\"\r\n                }`,\r\n                boxShadow: \"0 1px 4px rgba(0,0,0,0.05)\",\r\n              }}\r\n            >\r\n              <Flex align=\"center\" gap={12} style={{ width: \"100%\" }}>\r\n                {/* 左侧状态和优先级指示器 */}\r\n                <Flex vertical align=\"center\">\r\n                  {item.completed ? (\r\n                    <Flex\r\n                      align=\"center\"\r\n                      justify=\"center\"\r\n                      style={{\r\n                        width: 22,\r\n                        height: 22,\r\n                        borderRadius: \"50%\",\r\n                        background: \"#52c41a\",\r\n                      }}\r\n                    >\r\n                      <CheckOutlined\r\n                        style={{ color: \"#fff\", fontSize: 12 }}\r\n                      />\r\n                    </Flex>\r\n                  ) : (\r\n                    <div\r\n                      style={{\r\n                        width: 18,\r\n                        height: 18,\r\n                        borderRadius: \"50%\",\r\n                        border: `2px solid ${\r\n                          item.priority === \"high\"\r\n                            ? \"#ff4d4f\"\r\n                            : item.priority === \"medium\"\r\n                            ? \"#faad14\"\r\n                            : \"#8c8c8c\"\r\n                        }`,\r\n                      }}\r\n                    />\r\n                  )}\r\n\r\n                  <div\r\n                    style={{\r\n                      width: 2,\r\n                      height: 24,\r\n                      background: \"#f0f0f0\",\r\n                      marginTop: 4,\r\n                    }}\r\n                  />\r\n                </Flex>\r\n\r\n                {/* 任务信息区 */}\r\n                <Flex vertical style={{ flex: 1 }}>\r\n                  <Text\r\n                    style={{\r\n                      fontSize: 14,\r\n                      fontWeight:\r\n                        item.priority === \"high\" ? 500 : \"normal\",\r\n                      textDecoration: item.completed\r\n                        ? \"line-through\"\r\n                        : \"none\",\r\n                      color: item.completed ? \"#8c8c8c\" : \"#262626\",\r\n                    }}\r\n                  >\r\n                    {item.name}\r\n                  </Text>\r\n\r\n                  {/* 显示创建日期 */}\r\n                  <Space align=\"center\" size={6} style={{ marginTop: 4 }}>\r\n                    <CalendarOutlined\r\n                      style={{\r\n                        fontSize: 12,\r\n                        color: \"#8c8c8c\",\r\n                      }}\r\n                    />\r\n                    <Text type=\"secondary\" style={{ fontSize: 12 }}>\r\n                      创建于: {item.createDate}\r\n                    </Text>\r\n                  </Space>\r\n                </Flex>\r\n\r\n                {/* 操作按钮区 */}\r\n                <Dropdown\r\n                  trigger={['click']}\r\n                  menu={{\r\n                    items: [\r\n                      {\r\n                        key: 'complete',\r\n                        label: item.completed ? '标记未完成' : '标记完成',\r\n                        icon: (\r\n                          <CheckOutlined \r\n                            style={{ \r\n                              color: item.completed ? '#8c8c8c' : '#52c41a',\r\n                              fontSize: 14 \r\n                            }} \r\n                          />\r\n                        )\r\n                      },\r\n                      {\r\n                        key: 'edit',\r\n                        label: '编辑任务',\r\n                        icon: <EditOutlined style={{ color: '#8c8c8c' }} />\r\n                      },\r\n                      {\r\n                        key: 'delete',\r\n                        label: '删除任务',\r\n                        icon: <DeleteOutlined style={{ color: '#ff4d4f' }} />,\r\n                        danger: true\r\n                      }\r\n                    ],\r\n                    onClick: ({ key }) => {\r\n                      if (key === \"complete\") {\r\n                        handleToggleTodoStatus(item.id);\r\n                      } else if (key === \"edit\") {\r\n                        setEditingTodoId(item.id);\r\n                        todoForm.setFieldsValue({\r\n                          name: item.name,\r\n                          priority: item.priority\r\n                        });\r\n                        setTodoModalVisible(true);\r\n                      } else if (key === \"delete\") {\r\n                        handleDeleteTodo(item.id);\r\n                      }\r\n                    }\r\n                  }}\r\n                >\r\n                  <Button \r\n                    type=\"text\" \r\n                    size=\"small\" \r\n                    icon={<MoreOutlined />} \r\n                    style={{ width: 32, height: 32 }} \r\n                  />\r\n                </Dropdown>\r\n              </Flex>\r\n            </List.Item>\r\n          );\r\n        }}\r\n      />\r\n\r\n      {/* 待办事项表单模态框 */}\r\n      <Modal\r\n        title={editingTodoId ? \"编辑待办事项\" : \"新增待办事项\"}\r\n        open={todoModalVisible}\r\n        onCancel={() => {\r\n          setTodoModalVisible(false);\r\n          todoForm.resetFields();\r\n        }}\r\n        onOk={() => {\r\n          todoForm.submit();\r\n        }}\r\n        centered\r\n        destroyOnClose\r\n        footer={[\r\n          <Button key=\"cancel\" onClick={() => setTodoModalVisible(false)}>\r\n            取消\r\n          </Button>,\r\n          <Button\r\n            key=\"submit\"\r\n            type=\"primary\"\r\n            onClick={() => {\r\n              todoForm.submit();\r\n            }}\r\n            style={{\r\n              background: \"#1890ff\",\r\n              borderColor: \"#1890ff\",\r\n              boxShadow: \"0 2px 4px rgba(24, 144, 255, 0.3)\",\r\n            }}\r\n          >\r\n            {editingTodoId ? \"更新任务\" : \"创建任务\"}\r\n          </Button>,\r\n        ]}\r\n      >\r\n        <Form\r\n          form={todoForm}\r\n          layout=\"vertical\"\r\n          onFinish={handleAddOrUpdateTodo}\r\n          autoComplete=\"off\"\r\n        >\r\n          <Form.Item\r\n            name=\"name\"\r\n            label=\"任务名称\"\r\n            rules={[{ required: true, message: \"请输入任务名称\" }]}\r\n          >\r\n            <Input\r\n              placeholder=\"请输入任务名称\"\r\n              size=\"large\"\r\n              style={{ borderRadius: 6 }}\r\n            />\r\n          </Form.Item>\r\n\r\n          <Form.Item\r\n            name=\"priority\"\r\n            label=\"优先级\"\r\n            initialValue=\"medium\"\r\n            rules={[{ required: true, message: \"请选择优先级\" }]}\r\n          >\r\n            <Select\r\n              size=\"large\"\r\n              options={[\r\n                { value: \"high\", label: \"高优先级\" },\r\n                { value: \"medium\", label: \"中优先级\" },\r\n                { value: \"low\", label: \"低优先级\" },\r\n              ]}\r\n              style={{ borderRadius: 6 }}\r\n            />\r\n          </Form.Item>\r\n        </Form>\r\n      </Modal>\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport default TodoManagement;"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,iDACA;IACE,SAAS;;;;;;wCC+iBb;;;2BAAA;;;;;;oFAljB2C;yCAqBpC;yCACqB;0CAUrB;;;;;;;;;;YAEP,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAU;YAC3B,MAAM,EAAE,OAAO,EAAE,GAAG,UAAI;YASxB,MAAM,iBAAgD,CAAC;;gBACrD,aAAa;gBACb,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAiB,EAAE;gBACrE,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAoB;oBAC5D,mBAAmB;oBACnB,qBAAqB;oBACrB,kBAAkB;oBAClB,YAAY;oBACZ,gBAAgB;oBAChB,sBAAsB;gBACxB;gBACA,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;gBACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,eAAQ,EAAgB;gBAElD,WAAW;gBACX,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,eAAQ,EAAC;gBACzD,MAAM,CAAC,SAAS,GAAG,UAAI,CAAC,OAAO;gBAC/B,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAgB;gBAElE,QAAQ;gBACR,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EACxC;gBAEF,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAC;gBAE7C,WAAW;gBACX,IAAA,gBAAS,EAAC;oBACR,MAAM,gBAAgB;wBACpB,IAAI;4BACF,WAAW;4BACX,SAAS;4BAET,MAAM,CAAC,OAAO,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;gCACvC,iBAAW,CAAC,YAAY;gCACxB,iBAAW,CAAC,YAAY;6BACzB;4BAED,iBAAiB;4BACjB,aAAa;wBACf,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,eAAe;4BAC7B,SAAS;wBACX,SAAU;4BACR,WAAW;wBACb;oBACF;oBAEA;gBACF,GAAG,EAAE;gBAEL,mBAAmB;gBACnB,MAAM,wBAAwB,cAAc,MAAM,CAAC,CAAC;oBAClD,SAAS;oBACT,IAAI,cAAc,aAAa,KAAK,MAAM,KAAK,GAAG,OAAO;oBACzD,IAAI,cAAc,eAAe,KAAK,MAAM,KAAK,GAAG,OAAO;oBAE3D,WAAW;oBACX,IACE,cACA,CAAC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,KAEzD,OAAO;oBAGT,OAAO;gBACT;gBAEA,WAAW;gBACX,MAAM,yBAAyB,OAAO;oBACpC,IAAI;wBACF,MAAM,OAAO,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;wBAC9C,IAAI,CAAC,MAAM;wBAEX,MAAM,YAAY,KAAK,MAAM,KAAK,IAAI,IAAI;wBAC1C,MAAM,iBAAW,CAAC,UAAU,CAAC,IAAI;4BAAE,QAAQ;wBAAU;wBAErD,SAAS;wBACT,iBACE,cAAc,GAAG,CAAC,CAAC,OACjB,KAAK,EAAE,KAAK,KAAK;gCAAE,GAAG,IAAI;gCAAE,QAAQ;4BAAU,IAAI;wBAItD,SAAS;wBACT,MAAM,QAAQ,MAAM,iBAAW,CAAC,YAAY;wBAC5C,aAAa;wBAEb,aAAO,CAAC,OAAO,CAAC,cAAc,IAAI,UAAU;oBAC9C,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAC3B,aAAO,CAAC,KAAK,CAAC;oBAChB;gBACF;gBAEA,MAAM,wBAAwB,OAAO;oBACnC,IAAI;wBACF,IAAI,eAAe;4BACjB,WAAW;4BACX,MAAM,cAAc,MAAM,iBAAW,CAAC,UAAU,CAAC,eAAe;gCAC9D,OAAO,OAAO,IAAI;gCAClB,UAAU,OAAO,QAAQ;4BAC3B;4BAEA,iBACE,cAAc,GAAG,CAAC,CAAC,OACjB,KAAK,EAAE,KAAK,gBAAgB,cAAc;4BAG9C,aAAO,CAAC,OAAO,CAAC;wBAClB,OAAO;4BACL,UAAU;4BACV,MAAM,UAAU,MAAM,iBAAW,CAAC,UAAU,CAAC;gCAC3C,OAAO,OAAO,IAAI;gCAClB,UAAU,OAAO,QAAQ;4BAC3B;4BAEA,iBAAiB;gCAAC;mCAAY;6BAAc;4BAC5C,aAAO,CAAC,OAAO,CAAC;wBAClB;wBAEA,SAAS;wBACT,MAAM,QAAQ,MAAM,iBAAW,CAAC,YAAY;wBAC5C,aAAa;wBAEb,aAAa;wBACb,oBAAoB;wBACpB,iBAAiB;wBACjB,SAAS,WAAW;oBACtB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;wBACzB,aAAO,CAAC,KAAK,CAAC;oBAChB;gBACF;gBAEA,MAAM,mBAAmB,OAAO;oBAC9B,IAAI;wBACF,MAAM,iBAAW,CAAC,UAAU,CAAC;wBAC7B,iBAAiB,cAAc,MAAM,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;wBAE5D,SAAS;wBACT,MAAM,QAAQ,MAAM,iBAAW,CAAC,YAAY;wBAC5C,aAAa;wBAEb,aAAO,CAAC,OAAO,CAAC;oBAClB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;wBACzB,aAAO,CAAC,KAAK,CAAC;oBAChB;gBACF;gBAEA,qBACE,2BAAC,UAAI;oBACH,WAAU;oBACV,OAAO;wBACL,cAAc;wBACd,WAAW;wBACX,QAAQ;wBACR,YAAY;oBACd;oBACA,qBACE,2BAAC,UAAI;wBAAC,SAAQ;wBAAgB,OAAM;kCAClC,cAAA,2BAAC;4BAAK,MAAM;sCAAC;;;;;;;;;;;;sCAKjB,2BAAC,UAAI;4BACH,SAAQ;4BACR,OAAM;4BACN,OAAO;gCAAE,cAAc;gCAAI,KAAK;gCAAI,UAAU;4BAAO;;8CAErD,2BAAC,WAAK;oCAAC,MAAM;oCAAI,OAAO;wCAAE,MAAM;wCAAG,UAAU;oCAAI;;sDAE/C,2BAAC,WAAK,CAAC,MAAM;4CACX,aAAY;4CACZ,UAAU;4CACV,sBAAQ,2BAAC,qBAAc;;;;;4CACvB,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,OAAO;gDAAE,MAAM;4CAAE;;;;;;sDAInB,2BAAC,YAAM;4CACL,MAAK;4CACL,oBAAM,2BAAC,mBAAY;;;;;4CACnB,SAAS;gDACP,iBAAiB;gDACjB,SAAS,WAAW;gDACpB,oBAAoB;4CACtB;4CACA,OAAO;gDACL,YAAY;gDACZ,aAAa;gDACb,WAAW;gDACX,YAAY;4CACd;sDACD;;;;;;;;;;;;8CAKH,2BAAC,aAAO;oCAAC,MAAK;oCAAW,OAAO;wCAAE,QAAQ;wCAAI,iBAAiB;oCAAU;;;;;;8CAEzE,2BAAC,WAAK;oCAAC,OAAM;oCAAS,MAAM;;sDAE1B,2BAAC,WAAK;4CAAC,MAAM;;8DACX,2BAAC,aAAO;oDAAC,OAAM;8DACb,cAAA,2BAAC,UAAI;wDAAC,OAAM;;0EACV,2BAAC;gEACC,OAAO;oEACL,OAAO;oEACP,QAAQ;oEACR,cAAc;oEACd,YAAY;oEACZ,aAAa;gEACf;;;;;;0EAEF,2BAAC;gEAAK,OAAO;oEAAE,UAAU;oEAAI,YAAY;gEAAI;0EAAG;;;;;;;;;;;;;;;;;8DAMpD,2BAAC,aAAO;oDAAC,OAAM;8DACb,cAAA,2BAAC,UAAI;wDAAC,OAAM;;0EACV,2BAAC;gEACC,OAAO;oEACL,OAAO;oEACP,QAAQ;oEACR,cAAc;oEACd,YAAY;oEACZ,aAAa;gEACf;;;;;;0EAEF,2BAAC;gEAAK,OAAO;oEAAE,UAAU;oEAAI,YAAY;gEAAI;0EAAG;;;;;;;;;;;;;;;;;8DAMpD,2BAAC,aAAO;oDAAC,OAAM;8DACb,cAAA,2BAAC,UAAI;wDAAC,OAAM;;0EACV,2BAAC;gEACC,OAAO;oEACL,OAAO;oEACP,QAAQ;oEACR,cAAc;oEACd,YAAY;oEACZ,aAAa;gEACf;;;;;;0EAEF,2BAAC;gEAAK,OAAO;oEAAE,UAAU;oEAAI,YAAY;gEAAI;0EAAG;;;;;;;;;;;;;;;;;;;;;;;sDAOtD,2BAAC,aAAO;4CAAC,MAAK;4CAAW,OAAO;gDAAE,QAAQ;gDAAI,iBAAiB;4CAAU;;;;;;sDAGzE,2BAAC,aAAO;4CAAC,OAAM;sDACb,cAAA,2BAAC,cAAQ;gDACP,SAAS;gDACT,MAAK;gDACL,OAAO;oDAAE,OAAO;gDAAI;gDACpB,aAAY;;;;;;;;;;;;;;;;;;;;;;;sCAOpB,2BAAC,UAAI;4BACH,WAAW;4BACX,UAAU,CAAC,MACT,aAAa;4BAEf,MAAK;4BACL,OAAO;gCAAE,cAAc;4BAAE;;8CAEzB,2BAAC;oCAAQ,KAAI;mCAAS;;;;;8CACtB,2BAAC;oCAAQ,KAAI;mCAAU;;;;;8CACvB,2BAAC;oCAAQ,KAAI;mCAAU;;;;;;;;;;;sCAIzB,2BAAC,UAAI;4BACH,YAAY;4BACZ,YAAY,CAAC;gCACX,qBACE,2BAAC,UAAI,CAAC,IAAI;oCACR,WAAU;oCACV,OAAO;wCACL,SAAS;wCACT,cAAc;wCACd,cAAc;wCACd,YAAY;wCACZ,SAAS,KAAK,SAAS,GAAG,MAAM;wCAChC,YAAY,CAAC,UAAU,EACrB,KAAK,SAAS,GACV,YACA,KAAK,QAAQ,KAAK,SAClB,YACA,KAAK,QAAQ,KAAK,WAClB,YACA,UACL,CAAC;wCACF,WAAW;oCACb;8CAEA,cAAA,2BAAC,UAAI;wCAAC,OAAM;wCAAS,KAAK;wCAAI,OAAO;4CAAE,OAAO;wCAAO;;0DAEnD,2BAAC,UAAI;gDAAC,QAAQ;gDAAC,OAAM;;oDAClB,KAAK,SAAS,iBACb,2BAAC,UAAI;wDACH,OAAM;wDACN,SAAQ;wDACR,OAAO;4DACL,OAAO;4DACP,QAAQ;4DACR,cAAc;4DACd,YAAY;wDACd;kEAEA,cAAA,2BAAC,oBAAa;4DACZ,OAAO;gEAAE,OAAO;gEAAQ,UAAU;4DAAG;;;;;;;;;;+EAIzC,2BAAC;wDACC,OAAO;4DACL,OAAO;4DACP,QAAQ;4DACR,cAAc;4DACd,QAAQ,CAAC,UAAU,EACjB,KAAK,QAAQ,KAAK,SACd,YACA,KAAK,QAAQ,KAAK,WAClB,YACA,UACL,CAAC;wDACJ;;;;;;kEAIJ,2BAAC;wDACC,OAAO;4DACL,OAAO;4DACP,QAAQ;4DACR,YAAY;4DACZ,WAAW;wDACb;;;;;;;;;;;;0DAKJ,2BAAC,UAAI;gDAAC,QAAQ;gDAAC,OAAO;oDAAE,MAAM;gDAAE;;kEAC9B,2BAAC;wDACC,OAAO;4DACL,UAAU;4DACV,YACE,KAAK,QAAQ,KAAK,SAAS,MAAM;4DACnC,gBAAgB,KAAK,SAAS,GAC1B,iBACA;4DACJ,OAAO,KAAK,SAAS,GAAG,YAAY;wDACtC;kEAEC,KAAK,IAAI;;;;;;kEAIZ,2BAAC,WAAK;wDAAC,OAAM;wDAAS,MAAM;wDAAG,OAAO;4DAAE,WAAW;wDAAE;;0EACnD,2BAAC,uBAAgB;gEACf,OAAO;oEACL,UAAU;oEACV,OAAO;gEACT;;;;;;0EAEF,2BAAC;gEAAK,MAAK;gEAAY,OAAO;oEAAE,UAAU;gEAAG;;oEAAG;oEACxC,KAAK,UAAU;;;;;;;;;;;;;;;;;;;0DAM3B,2BAAC,cAAQ;gDACP,SAAS;oDAAC;iDAAQ;gDAClB,MAAM;oDACJ,OAAO;wDACL;4DACE,KAAK;4DACL,OAAO,KAAK,SAAS,GAAG,UAAU;4DAClC,oBACE,2BAAC,oBAAa;gEACZ,OAAO;oEACL,OAAO,KAAK,SAAS,GAAG,YAAY;oEACpC,UAAU;gEACZ;;;;;;wDAGN;wDACA;4DACE,KAAK;4DACL,OAAO;4DACP,oBAAM,2BAAC,mBAAY;gEAAC,OAAO;oEAAE,OAAO;gEAAU;;;;;;wDAChD;wDACA;4DACE,KAAK;4DACL,OAAO;4DACP,oBAAM,2BAAC,qBAAc;gEAAC,OAAO;oEAAE,OAAO;gEAAU;;;;;;4DAChD,QAAQ;wDACV;qDACD;oDACD,SAAS,CAAC,EAAE,GAAG,EAAE;wDACf,IAAI,QAAQ,YACV,uBAAuB,KAAK,EAAE;6DACzB,IAAI,QAAQ,QAAQ;4DACzB,iBAAiB,KAAK,EAAE;4DACxB,SAAS,cAAc,CAAC;gEACtB,MAAM,KAAK,IAAI;gEACf,UAAU,KAAK,QAAQ;4DACzB;4DACA,oBAAoB;wDACtB,OAAO,IAAI,QAAQ,UACjB,iBAAiB,KAAK,EAAE;oDAE5B;gDACF;0DAEA,cAAA,2BAAC,YAAM;oDACL,MAAK;oDACL,MAAK;oDACL,oBAAM,2BAAC,mBAAY;;;;;oDACnB,OAAO;wDAAE,OAAO;wDAAI,QAAQ;oDAAG;;;;;;;;;;;;;;;;;;;;;;4BAM3C;;;;;;sCAIF,2BAAC,WAAK;4BACJ,OAAO,gBAAgB,WAAW;4BAClC,MAAM;4BACN,UAAU;gCACR,oBAAoB;gCACpB,SAAS,WAAW;4BACtB;4BACA,MAAM;gCACJ,SAAS,MAAM;4BACjB;4BACA,QAAQ;4BACR,cAAc;4BACd,QAAQ;8CACN,2BAAC,YAAM;oCAAc,SAAS,IAAM,oBAAoB;8CAAQ;mCAApD;;;;;8CAGZ,2BAAC,YAAM;oCAEL,MAAK;oCACL,SAAS;wCACP,SAAS,MAAM;oCACjB;oCACA,OAAO;wCACL,YAAY;wCACZ,aAAa;wCACb,WAAW;oCACb;8CAEC,gBAAgB,SAAS;mCAXtB;;;;;6BAaP;sCAED,cAAA,2BAAC,UAAI;gCACH,MAAM;gCACN,QAAO;gCACP,UAAU;gCACV,cAAa;;kDAEb,2BAAC,UAAI,CAAC,IAAI;wCACR,MAAK;wCACL,OAAM;wCACN,OAAO;4CAAC;gDAAE,UAAU;gDAAM,SAAS;4CAAU;yCAAE;kDAE/C,cAAA,2BAAC,WAAK;4CACJ,aAAY;4CACZ,MAAK;4CACL,OAAO;gDAAE,cAAc;4CAAE;;;;;;;;;;;kDAI7B,2BAAC,UAAI,CAAC,IAAI;wCACR,MAAK;wCACL,OAAM;wCACN,cAAa;wCACb,OAAO;4CAAC;gDAAE,UAAU;gDAAM,SAAS;4CAAS;yCAAE;kDAE9C,cAAA,2BAAC,YAAM;4CACL,MAAK;4CACL,SAAS;gDACP;oDAAE,OAAO;oDAAQ,OAAO;gDAAO;gDAC/B;oDAAE,OAAO;oDAAU,OAAO;gDAAO;gDACjC;oDAAE,OAAO;oDAAO,OAAO;gDAAO;6CAC/B;4CACD,OAAO;gDAAE,cAAc;4CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOvC;eApgBM;;oBAgBe,UAAI,CAAC;;;iBAhBpB;gBAsgBN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;ID/iBD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,uCAAsC;YAAC;YAAU;SAAsC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,8BAA6B;YAAC;SAAmB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;YAAS;SAAyB;QAAC,mCAAkC;YAAC;YAAS;SAAyB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,iDAAgD;YAAC;SAAgD;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,kCAAiC;YAAC;SAAwB;QAAC,wCAAuC;YAAC;SAAuC;IAAA;;AACh/B"}